# API接口修正总结

## 🔍 问题分析

基于华润项目 `/Users/<USER>/androidproject/huarun-user/src/services/api/` 的实际实现，发现了以下问题：

### 1. 验证码接口错误
- ❌ **错误**: 使用POST方法调用 `/sms/login`
- ✅ **正确**: 使用GET方法，参数通过query params传递

### 2. 请求参数重复问题
- ❌ **错误**: 认为医院代码不应该在params中传递
- ✅ **正确**: 华润项目中既通过header也通过params传递医院代码

### 3. 接口路径选择错误
- ❌ **错误**: 不清楚不同场景应该使用哪个验证码接口
- ✅ **正确**: 根据SMSType选择对应的接口路径

## 📋 华润项目API分析

### SMSService.ts 关键逻辑

```typescript
// 华润项目的验证码获取逻辑
export const getSMSCode = (mobile: string, type: SMSType) => {
  let url = "";
  switch (type) {
    case SMSType.ADD_CONTACT:
      url = WXApis.PatientSMSCode;        // /sms/patient
      break;
    case SMSType.LOGIN:
      url = WXApis.LoginSMSCode;          // /sms/login
      break;
    case SMSType.WECHAT:
      url = WXApis.WeChatSignupSMSCode;   // /sms/wechat
      break;
    case SMSType.USER_CHANGE_MOBILE:
      url = WXApis.UserChangeMobileSMSCode; // /sms/mobile
      break;
  }
  
  return netService({
    method: ReqMethods.GET,  // 👈 关键：使用GET方法
    url: url,
    params: {                // 👈 关键：参数通过params传递
      mobile: mobile
    }
  });
};
```

### NetUtil.ts 请求拦截器逻辑

```typescript
// 华润项目的请求拦截器
net.interceptors.request.use(
  config => {
    // 获取统一的参数
    const commonParams = {
      hospital: getHospitalCode()  // 👈 关键：医院代码通过params传递
    };

    // 合并参数
    config.params = {
      ...commonParams,
      ...config.params
    };

    return config;
  }
);
```

### getAxiosOption配置

```typescript
const getAxiosOption = () => {
  const config = {
    timeout: 40000,
    baseURL: baseUrl()
  };
  
  if (getHospitalCode()) {
    config.headers = {
      IH_HOSPITAL: getHospitalCode()  // 👈 关键：医院代码也通过header传递
    };
  }
  
  if (getMPAppId()) {
    config.headers.IH_APPID = getMPAppId();
  }
  
  return config;
};
```

## ✅ 修正后的实现

### 1. 验证码接口修正

```typescript
// 修正后的验证码获取函数
export const getSMSCode = async (mobile: string, type: SMSType = SMSType.LOGIN): Promise<void> => {
  let url = "";
  
  // 根据类型选择不同的接口，参考华润项目逻辑
  switch (type) {
    case SMSType.LOGIN:
      url = AUTH_APIS.LOGIN_SMS_CODE; // /sms/login
      break;
    case SMSType.BIND:
    case SMSType.SIGNUP:
      url = AUTH_APIS.WECHAT_SMS_CODE; // /sms/wechat
      break;
    default:
      url = AUTH_APIS.LOGIN_SMS_CODE;
      break;
  }
  
  // 使用GET方法，参数放在params中，参考华润项目
  return http.get(url, { mobile });
};
```

### 2. HTTP客户端修正

```typescript
// 修正后的请求拦截器
httpClient.interceptors.request.use(
  (config) => {
    // 添加认证头
    const token = getToken();
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }

    // 获取统一的参数 - 参考华润项目，既通过header也通过params传递医院代码
    const commonParams = {
      hospital: getHospitalCode()
    };

    // 合并参数
    config.params = {
      ...commonParams,
      ...config.params
    };

    return config;
  }
);
```

### 3. API端点配置修正

```typescript
// 修正后的API端点配置
export const AUTH_APIS = {
  // 获取登录短信验证码
  LOGIN_SMS_CODE: '/sms/login',
  // 获取微信注册短信验证码  
  WECHAT_SMS_CODE: '/sms/wechat',
  // 其他接口...
};
```

## 🔄 请求流程示例

### 获取登录验证码请求

**修正前（错误）:**
```
POST /sms/login?hospital=hfz
Headers: {
  IH_HOSPITAL: hfz
}
Body: {
  mobile: "13800138000",
  type: "LOGIN"
}
```

**修正后（正确）:**
```
GET /sms/login?hospital=hfz&mobile=13800138000
Headers: {
  IH_HOSPITAL: hfz,
  IH_APPID: wx123456 (如果有)
}
```

### 获取微信注册验证码请求

```
GET /sms/wechat?hospital=hfz&mobile=13800138000
Headers: {
  IH_HOSPITAL: hfz,
  IH_APPID: wx123456
}
```

## 📱 不同场景的API使用

### 1. 纯手机号登录（非微信环境）
```typescript
// 1. 获取验证码
await getSMSCode(mobile, SMSType.LOGIN);  // GET /sms/login

// 2. 登录
await loginBySMSCode(encryptedMobile, smsCode, 'H5_PATIENT');  // POST /login/sms
```

### 2. 微信用户绑定手机号
```typescript
// 1. 获取验证码
await getSMSCode(mobile, SMSType.BIND);  // GET /sms/wechat

// 2. 绑定手机号
await bindPhoneWechat(mobile, wxCode, smsCode);  // POST /wechat/bind
```

### 3. 微信用户注册
```typescript
// 1. 获取验证码
await getSMSCode(mobile, SMSType.SIGNUP);  // GET /sms/wechat

// 2. 注册
await signupBySMSCode(mobile, smsCode, wxCode);  // POST /signup
```

## 🎯 关键修正点总结

1. **✅ 验证码接口方法**: POST → GET
2. **✅ 验证码接口路径**: 根据SMSType选择正确路径
3. **✅ 参数传递方式**: 通过query params而不是request body
4. **✅ 医院代码传递**: 保持华润项目的双重传递方式（header + params）
5. **✅ 请求拦截器**: 恢复统一参数合并逻辑

## 🧪 测试建议

### 本地测试验证码接口

```bash
# 测试登录验证码接口
curl -X GET "https://test.taihealth.cn/api/sms/login?hospital=hfz&mobile=13800138000" \
  -H "IH_HOSPITAL: hfz"

# 测试微信验证码接口  
curl -X GET "https://test.taihealth.cn/api/sms/wechat?hospital=hfz&mobile=13800138000" \
  -H "IH_HOSPITAL: hfz" \
  -H "IH_APPID: wx123456"
```

### 浏览器开发者工具验证

1. 打开浏览器开发者工具的Network标签
2. 触发验证码发送
3. 检查请求方法是否为GET
4. 检查URL参数是否包含hospital和mobile
5. 检查请求头是否包含IH_HOSPITAL

## 🚀 部署注意事项

1. **确保后端API支持**: 验证后端确实支持GET方法的验证码接口
2. **配置正确的baseURL**: 确保`window.AppConfig.myBaseUrl`指向正确的API地址
3. **医院代码配置**: 确保`window.AppConfig.hospCode`配置正确
4. **微信AppId配置**: 确保微信相关配置正确

通过这些修正，现在的API调用应该与华润项目保持一致，解决401错误问题。