// config.js（自动生成）
window.AppConfig = {
  baseURL: 'https://dify.taihealth.cn/v1',
  apiKey: 'app-iycGJ8AYIanHB92CvlaQ6yHd',
  reportAppId: 'app-iycGJ8AYIanHB92CvlaQ6yHd',
  doctorAppId: 'app-iycGJ8AYIanHB92CvlaQ6yHd',
  VITE_APP_VERSION: '1.0.0',
  
  // 网络配置 - 参考华润项目
  myBaseUrl: 'https://test.taihealth.cn/api', // 后端API基础URL
  hospCode: 'hfz', // 默认医院代码，可通过URL参数覆盖
  
  // Dify API 配置
  DIFY_API_BASE_URL: 'https://api.dify.ai/v1',
  DIFY_API_KEY: 'app-xxx', // 替换为你的API Key
  DIFY_APP_ID: 'xxx', // 替换为你的App ID
  
  // 微信分享配置
  WECHAT_CONFIG: {
    // 微信公众号AppId（需要配置真实的AppId）
    APP_ID: 'your_wechat_appid',
    
    // 微信分享默认配置
    SHARE_DEFAULT: {
      title: 'AI健康助理 - 您的专业健康顾问',
      desc: '智能健康咨询、报告解读、症状评估，24小时为您的健康护航',
      imgUrl: '/health-icon.svg'
    },
    
    // 微信签名服务器地址（如果有独立的签名服务）
    SIGNATURE_API: '/api/wechat/signature',
    
    // 是否启用微信分享功能
    ENABLED: true,
    
    // 调试模式（开发时可以开启）
    DEBUG: false
  }
};
