# 健康助理应用实现指南

## 概述

本文档记录了基于华润用户项目的网络架构和认证流程的实现，包含了微信授权、手机号绑定、网络配置和分享功能的完整实现。

## 已实现功能

### 1. 网络配置和URL解析 ✅

**文件位置**: `src/utils/networkUtils.ts`

**功能特性**:
- 从URL参数或路径解析医院代码 (`hospCode`)
- 支持 `?hospital=xxx` 参数格式
- 支持 `hospcode-xxx` 路径格式
- 微信公众号AppId管理
- Token管理功能

**配置示例**:
```javascript
// public/config.js
window.AppConfig = {
  myBaseUrl: 'https://test.taihealth.cn/api',
  hospCode: 'hfz', // 默认医院代码
  // ... 其他配置
}
```

### 2. HTTP客户端封装 ✅

**文件位置**: `src/utils/httpClient.ts`

**功能特性**:
- 自动添加医院代码到请求头 (`IH_HOSPITAL`)
- 自动添加微信AppId到请求头 (`IH_APPID`)
- 自动添加认证Token (`Authorization: Bearer`)
- 统一错误处理和token过期处理
- 请求和响应拦截器

**使用示例**:
```typescript
import { http } from '../utils/httpClient';

// GET请求
const data = await http.get('/api/users');

// POST请求
const result = await http.post('/api/login', { username, password });
```

### 3. 微信授权和手机号绑定 ✅

**文件位置**: 
- `src/services/authService.ts` - 认证服务
- `src/hooks/useAuth.ts` - 认证状态管理
- `src/pages/PhoneBinding.tsx` - 手机号绑定页面

**认证流程**:
1. 检测微信浏览器环境
2. 获取微信授权码 (`code` 参数)
3. 尝试微信登录
4. 如果用户不存在，跳转到手机号绑定页面
5. 发送短信验证码
6. 绑定手机号或注册新用户
7. 登录成功，保存token

**使用示例**:
```typescript
import { useAuth } from '../hooks/useAuth';

const MyComponent = () => {
  const { 
    authStatus, 
    user, 
    login, 
    sendSMSCode, 
    bindPhone 
  } = useAuth();

  // 检查认证状态
  if (authStatus === AuthStatus.NEED_PHONE_BINDING) {
    // 显示手机号绑定UI
  }
  
  // 发送验证码
  await sendSMSCode('13800138000');
  
  // 绑定手机号
  await bindPhone('13800138000', '123456');
};
```

### 4. 微信分享功能 ✅

**文件位置**: 
- `src/utils/wechatShare.ts` - 微信分享工具
- `src/hooks/useWechatShare.ts` - 微信分享Hook

**功能特性**:
- 自动检测微信浏览器环境
- 获取微信JS-SDK签名配置
- 支持自定义分享标题、描述和图片
- 支持朋友圈和朋友分享
- 兼容新旧版本微信API

**使用示例**:
```typescript
import { useWechatShare } from '../hooks/useWechatShare';

const HomePage = () => {
  const { isWechat, shareReady, updateShareContent } = useWechatShare({
    title: '个人健康助理',
    desc: '您的专业健康顾问',
    imgUrl: '/health-icon.svg'
  });

  // 动态更新分享内容
  const handleUpdateShare = () => {
    updateShareContent({
      title: '新的分享标题',
      desc: '新的分享描述'
    });
  };
};
```

### 5. 路由保护 ✅

**文件位置**: `src/components/ProtectedRoute.tsx`

**功能特性**:
- 检查用户认证状态
- 自动重定向到登录/绑定页面
- 加载状态显示
- 支持自定义重定向路径

**使用示例**:
```typescript
import ProtectedRoute from '../components/ProtectedRoute';

// 在路由中使用
<Route path="/private" element={
  <ProtectedRoute>
    <PrivatePage />
  </ProtectedRoute>
} />
```

### 6. 数据加密工具 ✅

**文件位置**: `src/utils/encryption.ts`

**功能特性**:
- 手机号AES加密（简化版）
- 手机号格式验证
- 手机号脱敏显示
- 加密解密工具函数

## 配置说明

### 微信配置

在 `public/config.js` 中配置微信相关参数：

```javascript
WECHAT_CONFIG: {
  // 微信公众号AppId
  APP_ID: 'your_wechat_appid',
  
  // 分享默认配置
  SHARE_DEFAULT: {
    title: 'AI健康助理 - 您的专业健康顾问',
    desc: '智能健康咨询、报告解读、症状评估，24小时为您的健康护航',
    imgUrl: '/health-icon.svg'
  },
  
  // 微信签名服务器地址
  SIGNATURE_API: '/api/wechat/signature',
  
  // 是否启用微信分享功能
  ENABLED: true,
  
  // 调试模式
  DEBUG: false
}
```

### 网络配置

```javascript
// 后端API基础URL
myBaseUrl: 'https://test.taihealth.cn/api',

// 默认医院代码，可通过URL参数覆盖
hospCode: 'hfz',
```

## API接口说明

### 认证相关接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/wechat/login` | POST | 微信授权登录 |
| `/login/sms` | POST | 手机号验证码登录 |
| `/signup` | POST | 手机号验证码注册 |
| `/wechat/bind` | POST | 绑定微信和手机号 |
| `/sms/login` | POST | 获取短信验证码 |
| `/wechat/mp/jsconfig` | GET | 获取微信JS配置 |
| `/logout` | POST | 退出登录 |

### 请求头说明

| 请求头 | 说明 | 示例 |
|--------|------|------|
| `IH_HOSPITAL` | 医院代码 | `hfz` |
| `IH_APPID` | 微信公众号AppId | `wx1234567890` |
| `Authorization` | 认证Token | `Bearer eyJhbGciOiJIUzI1NiIs...` |

## URL参数说明

### 医院代码解析

1. **查询参数方式**: `https://example.com/?hospital=hfz`
2. **路径参数方式**: `https://example.com/hospcode-hfz/`

### 微信授权参数

- `code`: 微信授权码
- `appId`: 微信公众号AppId

## 部署注意事项

### 1. 微信配置

- 确保微信公众号配置了正确的授权域名
- 配置微信JS接口安全域名
- 部署微信签名服务（后端）

### 2. 网络配置

- 确保 `myBaseUrl` 指向正确的后端API地址
- 配置正确的医院代码 `hospCode`
- 确保CORS配置正确

### 3. HTTPS要求

- 微信JS-SDK要求HTTPS环境
- 生产环境必须使用HTTPS

## 扩展性设计

### 1. 多医院支持

通过URL参数动态切换医院：
```
https://example.com/?hospital=hospital_a
https://example.com/hospcode-hospital_b/
```

### 2. 多公众号支持

通过URL参数指定公众号AppId：
```
https://example.com/?appId=wx123456&hospital=hfz
```

### 3. 认证流程扩展

- 支持多种登录方式（微信、手机号、邮箱等）
- 支持第三方OAuth登录
- 支持生物识别认证

## 故障排查

### 1. 微信授权失败

- 检查微信公众号配置
- 检查授权域名设置
- 检查网络连接

### 2. 手机号绑定失败

- 检查短信服务配置
- 检查手机号格式
- 检查验证码有效期

### 3. 分享功能异常

- 检查微信JS-SDK配置
- 检查签名服务
- 检查分享域名配置

## 开发建议

1. **本地开发**: 使用ngrok等工具提供HTTPS代理
2. **调试模式**: 开启微信配置中的DEBUG模式
3. **错误处理**: 完善错误提示和用户引导
4. **性能优化**: 合理使用缓存和懒加载
5. **安全性**: 生产环境使用真正的AES加密

## 后续优化

1. 完善AES加密实现
2. 添加用户信息管理
3. 实现登录状态持久化
4. 添加更多认证方式
5. 优化错误处理和用户体验