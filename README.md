# 个人健康助理 (Health Assistant Agent)

基于React + TypeScript + Vite构建的现代化移动端健康管理H5应用，集成Dify AI智能体，提供专业的健康咨询和报告解读服务。

## ✨ 功能特性

### 🩺 报告解读智能体
- **拍照解读**: 实时拍摄体检报告，AI智能解读分析
- **照片解读**: 上传报告图片，获得专业医学解读
- 支持多种报告格式：血常规、生化、影像等
- 详细的健康指标分析和建议

### 👨‍⚕️ 全科医生智能体  
- **AI健康咨询**: 24小时在线智能医生，多轮对话咨询
- **症状自评**: 结构化症状记录，智能风险评估
- **趋势分析**: 健康数据追踪，可视化趋势分析
- 个性化健康建议和就医指导

### 📱 用户体验
- 响应式设计，完美适配移动端
- PWA支持，可安装到手机桌面
- 流畅的动画效果，优秀的交互体验
- 本地数据存储，保护隐私安全

### 🔗 微信分享功能
- **智能分享**: 集成微信JS-SDK，支持朋友圈和朋友分享
- **链接卡片**: 分享显示为精美的链接卡片，而非普通链接
- **自定义内容**: 每个页面可配置专属的分享标题和描述
- **自动适配**: 自动检测微信浏览器环境，无缝集成分享功能

## 🚀 技术栈

- **前端框架**: React 18 + TypeScript
- **构建工具**: Vite 4
- **UI样式**: Tailwind CSS
- **动画库**: Framer Motion
- **图标库**: Lucide React
- **路由**: React Router DOM
- **HTTP客户端**: Axios
- **日期处理**: Day.js
- **AI集成**: Dify API
- **微信分享**: Weixin JS-SDK

## 📋 系统要求

- Node.js >= 16.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0
- 现代浏览器支持ES2020+

## 🛠️ 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd health-assitant-agent
```

### 2. 安装依赖
```bash
# 使用npm
npm install

# 或使用yarn
yarn install
```

### 3. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件，填入Dify API配置
nano .env
```

### 4. 启动开发服务器
```bash
npm run dev
```

### 5. 打开浏览器访问
```
http://localhost:3000
```

## ⚙️ 环境配置

### Dify平台配置
```env
# Dify API基础配置
VITE_DIFY_BASE_URL=https://dify.taihealth.cn/v1
VITE_DIFY_API_KEY=your_dify_api_key

# 智能体应用ID
VITE_DIFY_REPORT_APP_ID=your_report_agent_app_id
VITE_DIFY_DOCTOR_APP_ID=your_doctor_agent_app_id
```

### 应用配置
```env
# 应用基础信息
VITE_APP_NAME=个人健康助理
VITE_APP_VERSION=1.0.0

# API配置
VITE_API_TIMEOUT=30000
VITE_API_RETRY_COUNT=3

# 文件上传配置
VITE_UPLOAD_MAX_SIZE=10
VITE_UPLOAD_MAX_FILES=5
```

### 微信分享配置
在 `public/config.js` 中配置微信分享相关参数：

```javascript
WECHAT_CONFIG: {
  // 微信公众号AppId
  APP_ID: 'your_wechat_appid',
  
  // 微信分享默认配置
  SHARE_DEFAULT: {
    title: 'AI健康助理 - 您的专业健康顾问',
    desc: '智能健康咨询、报告解读、症状评估，24小时为您的健康护航',
    imgUrl: '/health-icon.svg'
  },
  
  // 微信签名服务器地址
  SIGNATURE_API: '/api/wechat/signature',
  
  // 是否启用微信分享功能
  ENABLED: true,
  
  // 调试模式
  DEBUG: false
}
```

**注意**: 微信分享功能需要配置后端签名服务，详细实现请参考 [微信分享API指南](PRD/WECHAT_SHARE_API.md)

## 🔧 开发指南

### 项目结构
```
src/
├── components/          # 公共组件
│   └── Layout.tsx      # 布局组件
├── pages/              # 页面组件
│   ├── Home.tsx        # 首页
│   ├── ReportAnalysis.tsx    # 报告解读
│   ├── HealthConsultation.tsx # 健康咨询
│   ├── SymptomAssessment.tsx  # 症状自评
│   └── TrendAnalysis.tsx      # 趋势分析
├── utils/              # 工具函数
│   ├── api.ts          # API封装
│   └── index.ts        # 通用工具
├── config/             # 配置文件
│   └── index.ts        # 应用配置
├── App.tsx             # 根组件
├── main.tsx            # 入口文件
└── index.css           # 全局样式
```

### 代码规范
- 使用TypeScript严格模式
- 遵循React Hooks最佳实践
- 组件采用函数式编程
- 所有函数和组件都有详细中文注释
- 使用Prettier进行代码格式化

### API集成
```typescript
// Dify API调用示例
import { difyClient } from '@/utils/api'

const response = await difyClient.sendMessage(
  appId,           // 智能体应用ID
  message,         // 用户消息
  conversationId,  // 会话ID（可选）
  files           // 文件列表（可选）
)
```

## 🐳 Docker部署

### 快速部署

项目采用Docker临时容器构建模式，确保跨平台兼容性：

```bash
# 给脚本执行权限
chmod +x build.sh wrap.sh deploy.sh

# 一键部署 (版本号 环境)
./deploy.sh 1.0.0 dev
```

### 部署架构

```
┌─────────────────────────────────────────────────────────────┐
│                    阿里云容器镜像服务                          │
│  registry.cn-shanghai.aliyuncs.com/chos/                   │
│  └── health-assit-agent-frontend-{env}:{version}           │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      部署环境                                │
│  └── 容器: health-assit-agent-frontend-{env}               │
│      ├── 端口: 8060                                        │
│      ├── 重启策略: always                                   │
│      └── 时区: Asia/Shanghai                               │
└─────────────────────────────────────────────────────────────┘
```

### 部署流程

部署脚本按照以下步骤执行：

1. **构建前端应用**: 使用Docker临时容器构建，避免本地环境依赖
2. **构建Docker镜像**: 创建基于nginx的生产环境镜像
3. **启动容器**: 自动清理旧容器并启动新容器

### 分步部署

#### 1. 构建前端应用
```bash
# 使用Docker临时容器构建 (推荐)
./build.sh 1.0.0

# 或本地构建 (需要Node.js环境)
npm install
npm run build
```

#### 2. 构建Docker镜像
```bash
docker build --platform linux/amd64 \
  -f Dockerfile \
  --build-arg VERSION=1.0.0 \
  -t registry.cn-shanghai.aliyuncs.com/chos/health-assit-agent-frontend-dev:1.0.0 \
  .
```

#### 3. 启动容器
```bash
# 清理旧容器
docker rm -f health-assit-agent-frontend-dev 2>/dev/null || echo "Container not found, skip removal"

# 启动新容器
docker run --restart always -d \
  -p 8060:8060 \
  -e TZ=Asia/Shanghai \
  --name health-assit-agent-frontend-dev \
  registry.cn-shanghai.aliyuncs.com/chos/health-assit-agent-frontend-dev:1.0.0
```

### 镜像命名规范

```
registry.cn-shanghai.aliyuncs.com/chos/health-assit-agent-frontend-{environment}:{version}
```

示例：
- `registry.cn-shanghai.aliyuncs.com/chos/health-assit-agent-frontend-dev:1.0.0`

### 容器配置

- **端口**: 8060
- **容器名称**: health-assit-agent-frontend-{environment}
- **时区**: Asia/Shanghai
- **重启策略**: always

### 访问应用

部署完成后，可以通过以下方式访问：

```bash
# 访问应用
curl http://localhost:8060

# 检查容器状态
docker ps | grep health-assit-agent-frontend
```

### 核心脚本说明

| 脚本 | 功能 | 参数 | 说明 |
|------|------|------|------|
| **build.sh** | Docker临时容器构建 | `<version>` | 使用node:18-alpine容器构建前端应用 |
| **wrap.sh** | 容器内构建脚本 | `<version>` | 配置npm源，处理跨平台依赖问题 |
| **deploy.sh** | 一键部署脚本 | `<version> <env>` | 完整部署流程：构建→镜像→容器 |

### 技术特性

- ✅ **跨平台构建**: Docker临时容器解决ARM64/AMD64兼容性
- ✅ **自动化部署**: 一键完成构建、镜像创建、容器启动
- ✅ **容器管理**: 自动清理旧容器，配置重启策略
- ✅ **镜像优化**: 基于nginx:1.25-alpine，体积小，性能高
- ✅ **配置标准化**: 遵循阿里云镜像仓库命名规范

## 📊 性能优化

### 构建优化
- Vite构建优化，支持代码分割
- 静态资源压缩和缓存
- Tree-shaking去除未使用代码
- 图片资源优化

### 运行时优化
- React.memo优化组件渲染
- 懒加载和代码分割
- 本地存储减少网络请求
- Service Worker缓存策略

## 🛡️ 安全说明

### 数据安全
- 所有健康数据仅在本地存储
- 不会上传敏感个人信息到服务器
- API通信使用HTTPS加密
- 严格的内容安全策略(CSP)

### 隐私保护
- 用户数据本地化存储
- 可选择性数据清除
- 符合GDPR隐私保护规范
- 透明的数据使用说明

## � 最近更新

### v1.0.1 - UI优化修复 (2025-01-18)

**修复内容**：
- ✅ **错误提示显示问题**: 修复错误提示被底部输入框遮挡的问题，提高z-index确保用户能看到错误信息
- ✅ **输入框定位问题**: 修复首次进入AI健康咨询页面时输入框只显示一半的问题，优化安全区域适配
- ✅ **首页导航栏**: 移除首页不必要的sticky header，提供更简洁的用户体验
- ✅ **移动端适配**: 优化移动端安全区域处理，确保在各种设备上都能正常显示

**技术改进**：
- 使用CSS `env(safe-area-inset-bottom)` 动态适配设备安全区域
- 优化z-index层级管理，确保UI元素正确显示顺序
- 简化输入框定位逻辑，提高兼容性
- 修复React deprecated API警告

## �🔍 故障排除

### 常见问题

1. **Dify API连接失败**
   ```bash
   # 检查网络连接
   curl -X GET "https://dify.taihealth.cn/v1/info"
   
   # 验证API Key
   curl -H "Authorization: Bearer YOUR_API_KEY" \
        "https://dify.taihealth.cn/v1/info"
   ```

2. **构建失败**
   ```bash
   # 清除缓存重新安装
   rm -rf node_modules package-lock.json
   npm install
   
   # 检查Node.js版本
   node --version  # 应该 >= 16.0.0
   ```

3. **Docker构建问题**
   ```bash
   # 清除Docker缓存
   docker system prune -a
   
   # 重新构建
   docker build --no-cache -t health-assistant:latest .
   ```

### 日志调试
```bash
# 开启开发模式详细日志
VITE_DEV_LOG_LEVEL=debug npm run dev

# Docker容器日志
docker logs health-assist-agent-frontend-dev

# Nginx访问日志
docker exec health-assist-agent-frontend-dev tail -f /var/log/nginx/access.log
```
