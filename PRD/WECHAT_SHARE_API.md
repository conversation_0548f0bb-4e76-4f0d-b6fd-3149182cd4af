# 微信分享API实现指南

## 概述
为了在微信浏览器中实现链接卡片分享功能，需要后端提供微信JS-SDK签名服务。

## API接口说明

### 获取微信签名
**接口地址：** `POST /api/wechat/signature`

**请求参数：**
```json
{
  "url": "https://your-domain.com/current-page"
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "appId": "wx1234567890abcdef",
    "timestamp": 1640995200,
    "nonceStr": "random_string",
    "signature": "generated_signature"
  }
}
```

## 后端实现要求

### 1. 微信公众号配置
- 需要一个已认证的微信公众号
- 在公众号后台配置JS接口安全域名
- 获取AppID和AppSecret

### 2. 获取access_token
```javascript
// 示例：获取access_token
const getAccessToken = async () => {
  const response = await fetch(
    `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${APPID}&secret=${APPSECRET}`
  )
  const data = await response.json()
  return data.access_token
}
```

### 3. 获取jsapi_ticket
```javascript
// 示例：获取jsapi_ticket
const getJsapiTicket = async (accessToken) => {
  const response = await fetch(
    `https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=${accessToken}&type=jsapi`
  )
  const data = await response.json()
  return data.ticket
}
```

### 4. 生成签名
```javascript
// 示例：生成签名
const crypto = require('crypto')

const generateSignature = (ticket, nonceStr, timestamp, url) => {
  const string = `jsapi_ticket=${ticket}&noncestr=${nonceStr}&timestamp=${timestamp}&url=${url}`
  return crypto.createHash('sha1').update(string).digest('hex')
}
```

### 5. 完整的Node.js实现示例
```javascript
const express = require('express')
const crypto = require('crypto')
const app = express()

// 配置
const WECHAT_CONFIG = {
  APPID: 'your_wechat_appid',
  APPSECRET: 'your_wechat_appsecret'
}

// 缓存access_token和jsapi_ticket
let tokenCache = {
  access_token: null,
  jsapi_ticket: null,
  expires_at: 0
}

// 获取微信签名
app.post('/api/wechat/signature', async (req, res) => {
  try {
    const { url } = req.body
    
    if (!url) {
      return res.status(400).json({
        code: 400,
        message: 'URL参数不能为空'
      })
    }

    // 检查并更新token
    if (Date.now() > tokenCache.expires_at) {
      await refreshWechatTokens()
    }

    // 生成随机字符串和时间戳
    const nonceStr = Math.random().toString(36).substring(2)
    const timestamp = Math.floor(Date.now() / 1000)

    // 生成签名
    const signature = generateSignature(
      tokenCache.jsapi_ticket,
      nonceStr,
      timestamp,
      url
    )

    res.json({
      code: 200,
      message: 'success',
      data: {
        appId: WECHAT_CONFIG.APPID,
        timestamp,
        nonceStr,
        signature
      }
    })

  } catch (error) {
    console.error('生成微信签名失败:', error)
    res.status(500).json({
      code: 500,
      message: '服务器内部错误'
    })
  }
})

// 刷新微信tokens
const refreshWechatTokens = async () => {
  // 获取access_token
  const tokenResponse = await fetch(
    `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${WECHAT_CONFIG.APPID}&secret=${WECHAT_CONFIG.APPSECRET}`
  )
  const tokenData = await tokenResponse.json()
  
  if (!tokenData.access_token) {
    throw new Error('获取access_token失败')
  }

  // 获取jsapi_ticket
  const ticketResponse = await fetch(
    `https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=${tokenData.access_token}&type=jsapi`
  )
  const ticketData = await ticketResponse.json()
  
  if (!ticketData.ticket) {
    throw new Error('获取jsapi_ticket失败')
  }

  // 更新缓存（提前5分钟过期）
  tokenCache = {
    access_token: tokenData.access_token,
    jsapi_ticket: ticketData.ticket,
    expires_at: Date.now() + (tokenData.expires_in - 300) * 1000
  }
}

// 生成签名
const generateSignature = (ticket, nonceStr, timestamp, url) => {
  const string = `jsapi_ticket=${ticket}&noncestr=${nonceStr}&timestamp=${timestamp}&url=${url}`
  return crypto.createHash('sha1').update(string).digest('hex')
}

app.listen(3000, () => {
  console.log('微信签名服务启动成功，端口：3000')
})
```

## 配置说明

### 1. 修改公共配置
在 `public/config.js` 中配置你的微信公众号信息：

```javascript
WECHAT_CONFIG: {
  // 🔥 替换为你的微信公众号AppId
  APP_ID: 'wx1234567890abcdef',
  
  // 微信签名服务器地址
  SIGNATURE_API: '/api/wechat/signature',
  
  // 启用微信分享功能
  ENABLED: true,
  
  // 调试模式（开发时可以开启）
  DEBUG: false
}
```

### 2. 域名配置
在微信公众号后台的"设置与开发" -> "公众号设置" -> "功能设置"中：
- 配置"JS接口安全域名"为你的网站域名

## 测试方法

1. 在微信浏览器中打开网站
2. 查看浏览器控制台日志
3. 点击微信右上角的分享按钮
4. 检查分享内容是否正确显示

## 注意事项

1. **域名必须一致**：签名时使用的URL必须与当前页面URL完全一致（不包括#后面的内容）
2. **HTTPS要求**：生产环境必须使用HTTPS协议
3. **缓存策略**：access_token和jsapi_ticket有效期为7200秒，需要妥善缓存
4. **错误处理**：需要处理微信API调用失败的情况
5. **安全性**：不要在前端暴露AppSecret

## 常见问题

### 1. signature签名错误
- 检查URL是否包含#fragment
- 确认域名配置是否正确
- 验证签名算法实现

### 2. invalid url domain
- 检查JS接口安全域名配置
- 确认域名格式（不要包含协议和端口）

### 3. access_token过期
- 实现自动刷新机制
- 避免频繁调用微信API 