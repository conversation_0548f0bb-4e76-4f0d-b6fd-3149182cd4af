import { AnimatePresence, motion } from 'framer-motion'
import React from 'react'
import { Route, Routes, useLocation } from 'react-router-dom'

// 页面组件
import HealthConsultation from '@/pages/HealthConsultation'
import Home from '@/pages/Home'
import Login from '@/pages/Login'
import PhoneBinding from '@/pages/PhoneBinding'
// import ReportAnalysis from '@/pages/ReportAnalysis'
import ReportAnalysisAgent from '@/pages/ReportAnalysisAgent'
import SymptomAssessment from '@/pages/SymptomAssessment'
import TrendAnalysis from '@/pages/TrendAnalysis'

// 布局组件
import { AuthProvider } from '@/components/AuthProvider'
import Layout from '@/components/Layout'
import ProtectedRoute from '@/components/ProtectedRoute'
import WechatDebugPanel from '@/components/WechatDebugPanel'

// 工具和配置
import { config, configUtils } from '@/config'
import { deviceUtils } from '@/utils'

/**
 * 主应用组件
 * 负责路由配置和全局状态管理
 */
function App() {
  const location = useLocation()

  // 应用初始化
  React.useEffect(() => {
    console.log('🚀 健康助理应用启动')
    console.log('📱 设备信息:', deviceUtils.getDeviceInfo())

    // 检查配置状态
    if (!configUtils.isDifyConfigured()) {
      console.warn('⚠️ Dify服务未配置，部分功能可能无法使用')
    }

    // 设置页面标题
    document.title = config.app.name
  }, [])

  // 页面切换动画配置
  const pageVariants = {
    initial: {
      opacity: 0,
      x: 20,
    },
    in: {
      opacity: 1,
      x: 0,
    },
    out: {
      opacity: 0,
      x: -20,
    },
  }

  const pageTransition = {
    type: 'tween',
    ease: 'anticipate',
    duration: 0.3,
  }

  return (
    <div className="App min-h-screen bg-gray-50">
      <AuthProvider>
        <Layout>
          <AnimatePresence mode="wait" initial={false}>
            <Routes location={location} key={location.pathname}>
              {/* 首页 - 需要认证 */}
              <Route
                path="/"
                element={
                  <ProtectedRoute>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <Home />
                    </motion.div>
                  </ProtectedRoute>
                }
              />

              {/* 报告解读页面 */}
              {/* <Route
              path="/report"
              element={
                <motion.div
                  initial="initial"
                  animate="in"
                  exit="out"
                  variants={pageVariants}
                  transition={pageTransition}
                >
                  <ReportAnalysis />
                </motion.div>
              }
            /> */}

              {/* 拍照解读智能体页面 - 需要认证 */}
              <Route
                path="/report-analysis"
                element={
                  <ProtectedRoute>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <ReportAnalysisAgent />
                    </motion.div>
                  </ProtectedRoute>
                }
              />

              {/* 健康咨询页面 - 需要认证 */}
              <Route
                path="/consultation"
                element={
                  <ProtectedRoute>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <HealthConsultation />
                    </motion.div>
                  </ProtectedRoute>
                }
              />

              {/* 症状自评页面 - 需要认证 */}
              <Route
                path="/assessment"
                element={
                  <ProtectedRoute>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <SymptomAssessment />
                    </motion.div>
                  </ProtectedRoute>
                }
              />

              {/* 趋势分析页面 - 需要认证 */}
              <Route
                path="/trend"
                element={
                  <ProtectedRoute>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <TrendAnalysis />
                    </motion.div>
                  </ProtectedRoute>
                }
              />

              {/* 手机号绑定页面 - 不需要认证 */}
              <Route
                path="/phone-binding"
                element={
                  <ProtectedRoute requireAuth={false}>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <PhoneBinding />
                    </motion.div>
                  </ProtectedRoute>
                }
              />

              {/* 登录页面 - 不需要认证 */}
              <Route
                path="/login"
                element={
                  <ProtectedRoute requireAuth={false}>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <Login />
                    </motion.div>
                  </ProtectedRoute>
                }
              />

              {/* 404页面 */}
              <Route
                path="*"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                    className="flex flex-col items-center justify-center min-h-96 text-center"
                  >
                    <div className="text-6xl mb-4">🤔</div>
                    <h2 className="text-2xl font-bold text-gray-800 mb-2">页面未找到</h2>
                    <p className="text-gray-600 mb-6">您访问的页面不存在或已被移动</p>
                    <button onClick={() => window.history.back()} className="btn-primary">
                      返回上一页
                    </button>
                  </motion.div>
                }
              />
            </Routes>
          </AnimatePresence>
        </Layout>

        {/* 微信调试面板（仅开发环境） */}
        {import.meta.env.DEV && <WechatDebugPanel />}
      </AuthProvider>
    </div>
  )
}

export default App
