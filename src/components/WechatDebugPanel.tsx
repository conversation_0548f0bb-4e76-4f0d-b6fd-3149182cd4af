/**
 * 微信调试面板组件
 * 用于在开发环境中显示微信配置状态和调试信息
 */
import React, { useState } from 'react'
import { useWechatConfigContext } from './WechatConfigProvider'
import { getDeviceInfo, getCurrentUrlForWechatConfig } from '../utils/networkUtils'

interface WechatDebugPanelProps {
  // 是否默认展开
  defaultExpanded?: boolean
  // 面板位置
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
}

export const WechatDebugPanel: React.FC<WechatDebugPanelProps> = ({
  defaultExpanded = false,
  position = 'bottom-right'
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded)
  const {
    status,
    isReady,
    isLoading,
    error,
    config,
    isInWechat,
    initConfig,
    resetConfig,
    retryConfig
  } = useWechatConfigContext()

  // 仅在开发环境显示
  if (!import.meta.env.DEV) {
    return null
  }

  const deviceInfo = getDeviceInfo()
  const currentUrl = getCurrentUrlForWechatConfig()

  const getPositionClasses = () => {
    const baseClasses = 'fixed z-50'
    switch (position) {
      case 'top-left':
        return `${baseClasses} top-4 left-4`
      case 'top-right':
        return `${baseClasses} top-4 right-4`
      case 'bottom-left':
        return `${baseClasses} bottom-4 left-4`
      case 'bottom-right':
      default:
        return `${baseClasses} bottom-4 right-4`
    }
  }

  const getStatusColor = () => {
    switch (status) {
      case 'success':
        return 'text-green-600'
      case 'error':
        return 'text-red-600'
      case 'loading':
        return 'text-blue-600'
      default:
        return 'text-gray-600'
    }
  }

  const getStatusIcon = () => {
    switch (status) {
      case 'success':
        return '✅'
      case 'error':
        return '❌'
      case 'loading':
        return '⏳'
      default:
        return '⚪'
    }
  }

  return (
    <div className={getPositionClasses()}>
      {/* 折叠/展开按钮 */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={`
          flex items-center gap-2 px-3 py-2 rounded-lg shadow-lg
          bg-white border border-gray-200 hover:bg-gray-50
          text-sm font-medium transition-all duration-200
          ${getStatusColor()}
        `}
      >
        <span>{getStatusIcon()}</span>
        <span>微信配置</span>
        <span className="text-xs text-gray-500">
          {isExpanded ? '▼' : '▶'}
        </span>
      </button>

      {/* 详细信息面板 */}
      {isExpanded && (
        <div className="mt-2 p-4 bg-white rounded-lg shadow-lg border border-gray-200 max-w-sm">
          {/* 基本状态 */}
          <div className="space-y-2 mb-4">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">状态:</span>
              <span className={`text-sm ${getStatusColor()}`}>
                {getStatusIcon()} {status}
              </span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">微信环境:</span>
              <span className={`text-sm ${isInWechat ? 'text-green-600' : 'text-red-600'}`}>
                {isInWechat ? '✅ 是' : '❌ 否'}
              </span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">配置就绪:</span>
              <span className={`text-sm ${isReady ? 'text-green-600' : 'text-gray-600'}`}>
                {isReady ? '✅ 是' : '❌ 否'}
              </span>
            </div>
          </div>

          {/* 错误信息 */}
          {error && (
            <div className="mb-4 p-2 bg-red-50 border border-red-200 rounded text-sm">
              <div className="font-medium text-red-800 mb-1">错误信息:</div>
              <div className="text-red-600">{error}</div>
            </div>
          )}

          {/* 配置详情 */}
          {config && (
            <div className="mb-4">
              <div className="text-sm font-medium mb-2">配置详情:</div>
              <div className="text-xs space-y-1 bg-gray-50 p-2 rounded">
                <div><strong>AppId:</strong> {config.config?.appId || 'N/A'}</div>
                <div><strong>URL:</strong> {config.url || 'N/A'}</div>
                <div><strong>时间戳:</strong> {config.config?.timestamp || 'N/A'}</div>
              </div>
            </div>
          )}

          {/* 设备信息 */}
          <div className="mb-4">
            <div className="text-sm font-medium mb-2">设备信息:</div>
            <div className="text-xs space-y-1 bg-gray-50 p-2 rounded">
              <div><strong>平台:</strong> {deviceInfo.platform}</div>
              <div><strong>移动端:</strong> {deviceInfo.isMobile ? '是' : '否'}</div>
              <div><strong>iOS:</strong> {deviceInfo.isIOS ? '是' : '否'}</div>
              <div><strong>Android:</strong> {deviceInfo.isAndroid ? '是' : '否'}</div>
              <div><strong>微信:</strong> {deviceInfo.isFromWeiXin ? '是' : '否'}</div>
            </div>
          </div>

          {/* URL信息 */}
          <div className="mb-4">
            <div className="text-sm font-medium mb-2">URL信息:</div>
            <div className="text-xs space-y-1 bg-gray-50 p-2 rounded">
              <div><strong>当前URL:</strong></div>
              <div className="break-all text-gray-600">{window.location.href}</div>
              <div><strong>签名URL:</strong></div>
              <div className="break-all text-gray-600">{currentUrl}</div>
              <div><strong>初始URL:</strong></div>
              <div className="break-all text-gray-600">{(window as any).initUrl || 'N/A'}</div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-2">
            <button
              onClick={() => initConfig(true)}
              disabled={isLoading}
              className="flex-1 px-3 py-2 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {isLoading ? '配置中...' : '重新配置'}
            </button>
            
            <button
              onClick={resetConfig}
              disabled={isLoading}
              className="flex-1 px-3 py-2 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 disabled:opacity-50"
            >
              重置
            </button>
            
            {error && (
              <button
                onClick={() => retryConfig()}
                disabled={isLoading}
                className="flex-1 px-3 py-2 text-xs bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50"
              >
                重试
              </button>
            )}
          </div>

          {/* 微信JS-SDK测试 */}
          {isReady && isInWechat && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="text-sm font-medium mb-2">微信功能测试:</div>
              <div className="flex gap-2">
                <button
                  onClick={() => {
                    if (window.wx) {
                      window.wx.getLocation({
                        type: 'wgs84',
                        success: (res: any) => {
                          console.log('📍 获取位置成功:', res)
                          alert(`位置获取成功: ${res.latitude}, ${res.longitude}`)
                        },
                        fail: (err: any) => {
                          console.error('📍 获取位置失败:', err)
                          alert(`位置获取失败: ${err.errMsg}`)
                        }
                      })
                    }
                  }}
                  className="flex-1 px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600"
                >
                  获取位置
                </button>
                
                <button
                  onClick={() => {
                    if (window.wx) {
                      window.wx.scanQRCode({
                        needResult: 1,
                        scanType: ["qrCode", "barCode"],
                        success: (res: any) => {
                          console.log('📷 扫码成功:', res)
                          alert(`扫码结果: ${res.resultStr}`)
                        },
                        fail: (err: any) => {
                          console.error('📷 扫码失败:', err)
                          alert(`扫码失败: ${err.errMsg}`)
                        }
                      })
                    }
                  }}
                  className="flex-1 px-2 py-1 text-xs bg-purple-500 text-white rounded hover:bg-purple-600"
                >
                  扫码
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default WechatDebugPanel
