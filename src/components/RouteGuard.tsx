/**
 * 路由守卫组件
 * 参考华润项目Routes.tsx的完整实现
 * 统一处理认证、微信授权、权限控制等逻辑
 */

import React, { useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { isFromWeiXin, getQueryVariableValue, getToken, getDeviceInfo } from '../utils/networkUtils'
import { performWeChatAuthFlow, WeChatAuthStatus } from '../services/wechatAuthService'
import { useAuth, AuthStatus } from '../hooks/useAuth'

// 路由配置接口
export interface RouteConfig {
  path: string
  requireAuth: boolean // 是否需要登录
  allowedRoles?: string[] // 允许的角色
  requirePhoneBinding?: boolean // 是否需要绑定手机号
  requireWechatAuth?: boolean // 是否需要微信授权
  allowGuest?: boolean // 是否允许游客访问
}

// 默认路由配置
const DEFAULT_ROUTES: { [key: string]: RouteConfig } = {
  '/': { path: '/', requireAuth: false, allowGuest: true },
  '/home': { path: '/home', requireAuth: true },
  '/health-consultation': { path: '/health-consultation', requireAuth: true },
  '/report-analysis': { path: '/report-analysis', requireAuth: true },
  '/symptom-assessment': { path: '/symptom-assessment', requireAuth: true },
  '/trend-analysis': { path: '/trend-analysis', requireAuth: true },
  '/phone-binding': { path: '/phone-binding', requireAuth: false, allowGuest: true },
  '/login': { path: '/login', requireAuth: false, allowGuest: true },
  '/privacy': { path: '/privacy', requireAuth: false, allowGuest: true },
  '/terms': { path: '/terms', requireAuth: false, allowGuest: true },
}

interface RouteGuardProps {
  children: React.ReactNode
  routeConfig?: RouteConfig
}

export const RouteGuard: React.FC<RouteGuardProps> = ({ children, routeConfig }) => {
  const location = useLocation()
  const navigate = useNavigate()
  const { authStatus, isLoading } = useAuth()
  const [isProcessing, setIsProcessing] = useState(false)
  const [debugInfo, setDebugInfo] = useState<any>(null)

  // 获取当前路由配置
  const getCurrentRouteConfig = (): RouteConfig => {
    if (routeConfig) return routeConfig

    const currentPath = location.pathname
    return (
      DEFAULT_ROUTES[currentPath] || {
        path: currentPath,
        requireAuth: true,
      }
    )
  }

  // 检查用户权限 (暂未使用，预留扩展)
  // const checkUserPermissions = (config: RouteConfig, userRoles?: string[]): boolean => {
  //   if (!config.allowedRoles || config.allowedRoles.length === 0) {
  //     return true; // 没有角色限制
  //   }
  //
  //   if (!userRoles || userRoles.length === 0) {
  //     return false; // 用户没有角色
  //   }
  //
  //   return config.allowedRoles.some(role => userRoles.includes(role));
  // };

  // 处理微信授权流程
  const handleWeChatAuth = async (): Promise<boolean> => {
    if (!isFromWeiXin()) {
      console.log('📱 非微信环境，跳过微信授权')
      return false
    }

    setIsProcessing(true)

    try {
      console.log('🔄 开始微信授权流程')
      const authResult = await performWeChatAuthFlow()

      console.log('🔍 微信授权结果:', authResult)

      switch (authResult.status) {
        case WeChatAuthStatus.SUCCESS:
          console.log('✅ 微信授权成功')
          return true

        case WeChatAuthStatus.NEED_PHONE_BINDING:
          console.log('📱 需要绑定手机号，跳转绑定页面')
          navigate('/phone-binding', {
            state: {
              from: location.pathname,
              wxCode: authResult.wxCode,
            },
            replace: true,
          })
          return false

        case WeChatAuthStatus.PROCESSING:
          console.log('⏳ 微信授权处理中...')
          return false

        case WeChatAuthStatus.NOT_IN_WECHAT:
          console.log('❌ 不在微信环境')
          return false

        case WeChatAuthStatus.ERROR:
        default:
          console.error('❌ 微信授权失败:', authResult.error)
          return false
      }
    } catch (error: any) {
      console.error('微信授权流程异常:', error)
      return false
    } finally {
      setIsProcessing(false)
    }
  }

  // 处理路由守卫逻辑
  const handleRouteGuard = async () => {
    const config = getCurrentRouteConfig()
    const currentPath = location.pathname

    console.log('🛡️ 路由守卫检查:', {
      path: currentPath,
      config,
      authStatus,
      isLoading,
      isFromWeiXin: isFromWeiXin(),
      hasWeChatCode: !!getQueryVariableValue('code'),
    })

    // 更新调试信息
    setDebugInfo({
      path: currentPath,
      config,
      authStatus,
      isLoading,
      deviceInfo: getDeviceInfo(),
      hasToken: !!getToken(),
      hasWeChatCode: !!getQueryVariableValue('code'),
      timestamp: new Date().toLocaleTimeString(),
    })

    // 如果正在加载，等待
    if (isLoading || authStatus === AuthStatus.LOADING) {
      console.log('⏳ 认证状态加载中，等待...')
      return
    }

    // 如果是游客允许的路由，直接通过
    if (config.allowGuest && !config.requireAuth) {
      console.log('👤 游客路由，直接通过')
      return
    }

    // 检查是否需要登录
    if (config.requireAuth) {
      switch (authStatus) {
        case AuthStatus.AUTHENTICATED:
          console.log('✅ 用户已认证，允许访问')
          break

        case AuthStatus.NEED_PHONE_BINDING:
          if (currentPath !== '/phone-binding') {
            console.log('📱 需要绑定手机号，跳转绑定页面')
            navigate('/phone-binding', {
              state: { from: currentPath },
              replace: true,
            })
          }
          break

        case AuthStatus.UNAUTHENTICATED:
          // 在微信环境中，尝试微信授权
          if (isFromWeiXin()) {
            console.log('🔄 尝试微信授权...')
            const wechatAuthSuccess = await handleWeChatAuth()
            if (!wechatAuthSuccess) {
              console.log('❌ 微信授权失败或需要其他处理')
            }
          } else {
            // 非微信环境，跳转到登录页面
            console.log('🔄 非微信环境，跳转登录页面')
            if (currentPath !== '/phone-binding' && currentPath !== '/login') {
              navigate('/phone-binding', {
                state: { from: currentPath },
                replace: true,
              })
            }
          }
          break

        default:
          console.log('❓ 未知认证状态:', authStatus)
      }
    }
  }

  // 监听路由和认证状态变化
  useEffect(() => {
    handleRouteGuard()
  }, [location.pathname, authStatus, isLoading])

  // 监听微信授权回调
  useEffect(() => {
    const wxCode = getQueryVariableValue('code')
    const wxState = getQueryVariableValue('state')

    if (wxCode && wxState && wxState.startsWith('health_assistant_')) {
      console.log('🔄 检测到微信授权回调:', { wxCode, wxState })
      // 微信授权回调由AuthProvider和useAuth自动处理

      // 清理URL参数
      const url = new URL(window.location.href)
      url.searchParams.delete('code')
      url.searchParams.delete('state')
      window.history.replaceState({}, '', url.toString())
    }
  }, [])

  // 渲染加载状态
  if (isLoading || isProcessing || authStatus === AuthStatus.LOADING) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-white flex items-center justify-center">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
          <p className="text-gray-600">{isProcessing ? '正在处理微信授权...' : '加载中...'}</p>
        </div>
      </div>
    )
  }

  // 渲染子组件
  return (
    <>
      {children}

      {/* 开发环境显示调试信息 */}
      {import.meta.env.DEV && debugInfo && (
        <div className="fixed bottom-4 right-4 bg-black bg-opacity-80 text-white text-xs p-3 rounded-lg max-w-sm">
          <div className="font-bold mb-2">🛡️ RouteGuard Debug</div>
          <div>路径: {debugInfo.path}</div>
          <div>认证状态: {debugInfo.authStatus}</div>
          <div>需要登录: {debugInfo.config?.requireAuth ? '是' : '否'}</div>
          <div>微信环境: {debugInfo.deviceInfo?.isFromWeiXin ? '是' : '否'}</div>
          <div>iOS: {debugInfo.deviceInfo?.isIOS ? '是' : '否'}</div>
          <div>Android: {debugInfo.deviceInfo?.isAndroid ? '是' : '否'}</div>
          <div>有Token: {debugInfo.hasToken ? '是' : '否'}</div>
          <div>有微信Code: {debugInfo.hasWeChatCode ? '是' : '否'}</div>
          <div>更新时间: {debugInfo.timestamp}</div>
        </div>
      )}
    </>
  )
}

export default RouteGuard
