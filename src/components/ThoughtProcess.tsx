import { AnimatePresence, motion } from 'framer-motion'
import { Brain, ChevronDown, ChevronUp } from 'lucide-react'
import React from 'react'

// 思考数据类型
interface AgentThought {
  id: string
  tool: string
  thought: string
}

interface ThoughtProcessProps {
  thoughts: string[] | AgentThought[]
  currentThought?: string
  isThinking: boolean
  collapsed: boolean
  onToggle: () => void
  className?: string
}

/**
 * 统一的AI思考过程展示组件
 * 适用于健康咨询、症状评估、报告解读等页面
 */
const ThoughtProcess: React.FC<ThoughtProcessProps> = ({
  thoughts,
  currentThought,
  isThinking,
  collapsed,
  onToggle,
  className = '',
}) => {
  // 如果没有思考过程且不在思考中，不显示组件
  if (thoughts.length === 0 && !currentThought && !isThinking) {
    return null
  }

  // 标准化思考数据格式
  const normalizeThoughts = (): AgentThought[] => {
    return thoughts.map((thought, index) => {
      if (typeof thought === 'string') {
        // 尝试解析字符串格式的思考过程
        try {
          if (thought.startsWith('{') && thought.endsWith('}')) {
            const parsed = JSON.parse(thought)
            return {
              id: `thought-${index}`,
              tool: parsed.tool || 'AI思考',
              thought: parsed.thought || thought,
            }
          }
        } catch (e) {
          // 解析失败，使用原始文本
        }

        // 检查是否包含工具信息
        if (thought.includes('"tool"')) {
          const toolMatch = thought.match(/"tool":"([^"]*)"/)
          const thoughtMatch = thought.match(/"thought":"([^"]*)"/)
          return {
            id: `thought-${index}`,
            tool: toolMatch ? toolMatch[1] : 'AI思考',
            thought: thoughtMatch ? thoughtMatch[1] : thought,
          }
        }

        return {
          id: `thought-${index}`,
          tool: 'AI思考',
          thought: thought,
        }
      } else {
        // 已经是 AgentThought 格式
        return thought
      }
    })
  }

  const normalizedThoughts = normalizeThoughts()

  return (
    <div className={`bg-white rounded-2xl border border-gray-200 ${className}`}>
      <button
        onClick={onToggle}
        className="flex items-center justify-between w-full p-4 hover:bg-purple-50 transition-colors rounded-t-2xl"
      >
        <div className="flex items-center space-x-2">
          <Brain className="w-5 h-5 text-purple-600" />
          <h3 className="font-medium text-purple-900">AI思考过程</h3>
          {isThinking && (
            <div className="flex items-center space-x-1 ml-2">
              <div className="w-1 h-1 bg-purple-500 rounded-full animate-bounce" />
              <div
                className="w-1 h-1 bg-purple-500 rounded-full animate-bounce"
                style={{ animationDelay: '0.1s' }}
              />
              <div
                className="w-1 h-1 bg-purple-500 rounded-full animate-bounce"
                style={{ animationDelay: '0.2s' }}
              />
            </div>
          )}
        </div>
        {normalizedThoughts.length > 0 && (
          <button className="text-sm text-purple-600 hover:text-purple-800 transition-colors">
            {collapsed ? '展开' : '收起'}
          </button>
        )}
        {collapsed ? (
          <ChevronDown className="w-4 h-4 text-purple-600" />
        ) : (
          <ChevronUp className="w-4 h-4 text-purple-600" />
        )}
      </button>

      <AnimatePresence mode="wait">
        {!collapsed && (
          <motion.div
            key="thought-content"
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="px-4 pb-4 space-y-3">
              {/* 已完成的思考过程 */}
              {normalizedThoughts.map((thought, index) => (
                <motion.div
                  key={thought.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4 border-l-4 border-purple-400"
                >
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-medium text-purple-600">{index + 1}</span>
                    </div>
                    <div className="flex-1">
                      <div className="text-xs text-purple-600 font-medium mb-1">{thought.tool}</div>
                      <p className="text-sm text-gray-700 leading-relaxed whitespace-pre-wrap break-words">
                        {/* 清理思考内容 */}
                        {thought.thought
                          .replace(/^<think>/gi, '')
                          .replace(/<\/think>$/gi, '')
                          .replace(/<\/?think>/gi, '')
                          .replace(/###THINKING_COMPLETED###/g, '')
                          .trim() || '正在思考...'}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}

              {/* 当前正在进行的思考 */}
              {currentThought && isThinking && (
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 border-l-4 border-blue-400"
                >
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
                    </div>
                    <div className="flex-1">
                      <div className="text-xs text-blue-600 font-medium mb-1">正在思考...</div>
                      <p className="text-sm text-blue-800 leading-relaxed whitespace-pre-wrap break-words">
                        {currentThought}
                        {/* 流式思考光标效果 */}
                        <motion.span
                          animate={{ opacity: [1, 0, 1] }}
                          transition={{ duration: 1, repeat: Infinity }}
                          className="inline-block w-1 h-4 bg-blue-500 ml-1"
                        />
                      </p>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* 思考状态提示 */}
              {isThinking && !currentThought && normalizedThoughts.length === 0 && (
                <div className="flex items-center justify-center space-x-2 text-purple-600 py-4">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600"></div>
                  <span className="text-sm">🧠 AI正在思考分析...</span>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default ThoughtProcess
