/**
 * 微信配置提供者组件
 * 在应用级别提供微信JS-SDK配置管理
 */
import React, { createContext, useContext, useEffect, ReactNode } from 'react'
import { useWechatConfig, UseWechatConfigReturn } from '../hooks/useWechatConfig'
import { initPageUrl, isFromWeiXin } from '../utils/networkUtils'

// 微信配置上下文接口
interface WechatConfigContextType extends UseWechatConfigReturn {
  // 是否在微信环境
  isInWechat: boolean
}

// 创建上下文
const WechatConfigContext = createContext<WechatConfigContextType | null>(null)

// 提供者组件属性接口
interface WechatConfigProviderProps {
  children: ReactNode
  // 是否启用调试模式
  debug?: boolean
  // 是否在非微信环境中显示警告
  showNonWechatWarning?: boolean
}

/**
 * 微信配置提供者组件
 */
export const WechatConfigProvider: React.FC<WechatConfigProviderProps> = ({
  children,
  debug = import.meta.env.DEV,
  showNonWechatWarning = false
}) => {
  // 使用微信配置Hook
  const wechatConfig = useWechatConfig({
    autoInit: true,
    refreshOnRouteChange: true,
    enableDebugLog: debug
  })

  const isInWechat = isFromWeiXin()

  // 初始化页面URL（用于iOS平台的微信签名）
  useEffect(() => {
    initPageUrl()
  }, [])

  // 调试信息
  useEffect(() => {
    if (debug) {
      console.log('🔧 [WechatConfigProvider] 状态更新:', {
        isInWechat,
        status: wechatConfig.status,
        isReady: wechatConfig.isReady,
        error: wechatConfig.error
      })
    }
  }, [debug, isInWechat, wechatConfig.status, wechatConfig.isReady, wechatConfig.error])

  // 非微信环境警告
  if (!isInWechat && showNonWechatWarning) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <div className="text-center p-6 bg-white rounded-lg shadow-lg max-w-sm mx-4">
          <div className="text-6xl mb-4">📱</div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">
            请在微信中打开
          </h2>
          <p className="text-gray-600 text-sm">
            此应用需要在微信客户端中使用
          </p>
        </div>
      </div>
    )
  }

  // 提供上下文值
  const contextValue: WechatConfigContextType = {
    ...wechatConfig,
    isInWechat
  }

  return (
    <WechatConfigContext.Provider value={contextValue}>
      {children}
    </WechatConfigContext.Provider>
  )
}

/**
 * 使用微信配置上下文的Hook
 */
export const useWechatConfigContext = (): WechatConfigContextType => {
  const context = useContext(WechatConfigContext)
  
  if (!context) {
    throw new Error('useWechatConfigContext must be used within a WechatConfigProvider')
  }
  
  return context
}

/**
 * 微信配置状态指示器组件
 */
export const WechatConfigIndicator: React.FC<{
  showOnlyErrors?: boolean
  className?: string
}> = ({ 
  showOnlyErrors = false,
  className = ''
}) => {
  const { status, isReady, error, isInWechat } = useWechatConfigContext()

  // 非微信环境不显示
  if (!isInWechat) {
    return null
  }

  // 仅显示错误且无错误时不显示
  if (showOnlyErrors && !error) {
    return null
  }

  // 已就绪且仅显示错误时不显示
  if (showOnlyErrors && isReady) {
    return null
  }

  const getStatusInfo = () => {
    switch (status) {
      case 'loading':
        return {
          icon: '⏳',
          text: '微信配置中...',
          bgColor: 'bg-blue-100',
          textColor: 'text-blue-800'
        }
      case 'success':
        return {
          icon: '✅',
          text: '微信配置成功',
          bgColor: 'bg-green-100',
          textColor: 'text-green-800'
        }
      case 'error':
        return {
          icon: '❌',
          text: error || '微信配置失败',
          bgColor: 'bg-red-100',
          textColor: 'text-red-800'
        }
      default:
        return {
          icon: '⚪',
          text: '微信配置待初始化',
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-800'
        }
    }
  }

  const statusInfo = getStatusInfo()

  return (
    <div className={`
      flex items-center gap-2 px-3 py-2 rounded-lg text-sm
      ${statusInfo.bgColor} ${statusInfo.textColor} ${className}
    `}>
      <span>{statusInfo.icon}</span>
      <span>{statusInfo.text}</span>
    </div>
  )
}

/**
 * 微信配置错误边界组件
 */
interface WechatConfigErrorBoundaryState {
  hasError: boolean
  error?: Error
}

export class WechatConfigErrorBoundary extends React.Component<
  { children: ReactNode; fallback?: ReactNode },
  WechatConfigErrorBoundaryState
> {
  constructor(props: { children: ReactNode; fallback?: ReactNode }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): WechatConfigErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('🚨 [WechatConfigErrorBoundary] 微信配置错误:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="flex items-center justify-center min-h-screen bg-gray-100">
          <div className="text-center p-6 bg-white rounded-lg shadow-lg max-w-sm mx-4">
            <div className="text-6xl mb-4">⚠️</div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">
              微信配置错误
            </h2>
            <p className="text-gray-600 text-sm mb-4">
              {this.state.error?.message || '微信配置初始化失败'}
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              重新加载
            </button>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

// 导出默认配置的提供者
export const WechatConfigProviderDefault: React.FC<{ children: ReactNode }> = ({ children }) => (
  <WechatConfigErrorBoundary>
    <WechatConfigProvider debug={import.meta.env.DEV}>
      {children}
    </WechatConfigProvider>
  </WechatConfigErrorBoundary>
)
