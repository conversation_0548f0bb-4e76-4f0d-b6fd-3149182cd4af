/**
 * 受保护的路由组件
 * 用于需要认证的页面，现在由AuthProvider统一处理认证逻辑
 */
import React from 'react'
import { AuthStatus } from '../hooks/useAuth'
import { useAuthContext } from './AuthProvider'

interface ProtectedRouteProps {
  children: React.ReactNode
  requireAuth?: boolean // 是否需要认证，默认true
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, requireAuth = true }) => {
  const { authStatus, isLoading } = useAuthContext()

  console.log('🛡️ ProtectedRoute - 状态检查:', { authStatus, isLoading, requireAuth })

  // 如果不需要认证，直接显示内容
  if (!requireAuth) {
    return <>{children}</>
  }

  // 🔥 修复：认证成功时直接显示内容，优先级最高
  if (authStatus === AuthStatus.AUTHENTICATED) {
    console.log('✅ ProtectedRoute - 用户已认证，显示内容')
    return <>{children}</>
  }

  // 🔥 修复：只在真正需要加载时显示加载状态
  if (isLoading || authStatus === AuthStatus.LOADING) {
    console.log('⏳ ProtectedRoute - 显示加载状态', { isLoading, authStatus })
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在验证身份...</p>
        </div>
      </div>
    )
  }

  // 🔥 修复：其他状态（未认证、需要绑定手机号）显示简短的处理状态
  // AuthProvider会处理跳转逻辑，这里不应该长时间显示
  console.log('🔄 ProtectedRoute - 等待AuthProvider处理跳转')
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
        <p className="text-gray-600 text-sm">处理中...</p>
      </div>
    </div>
  )
}

export default ProtectedRoute
