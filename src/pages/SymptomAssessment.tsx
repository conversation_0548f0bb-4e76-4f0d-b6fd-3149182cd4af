import { AnimatePresence, motion } from 'framer-motion'
import {
  Activity,
  AlertTriangle,
  Brain,
  CheckCircle,
  ChevronLeft,
  ChevronRight,
  Clock,
  Ear,
  Eye,
  Heart,
  Info,
  Send,
  Shield,
  Thermometer,
  XCircle,
} from 'lucide-react'
import React, { useEffect, useRef, useState } from 'react'
import { createPortal } from 'react-dom'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'

// 组件
import ThoughtProcess from '@/components/ThoughtProcess'

// 工具和API
import { config, configUtils } from '@/config'
import { storageUtils } from '@/utils'
import { difyClient } from '@/utils/api'

// 症状数据类型
interface Symptom {
  id: string
  name: string
  icon: React.ElementType
  category: string
  severity?: number
  duration?: string
  location?: string
  description?: string
  frequency?: string
}

interface AssessmentResult {
  riskLevel: 'low' | 'medium' | 'high' | 'urgent'
  summary: string
  suggestions: string[]
  urgency: string
  followUp: string
}

interface HistoryItem {
  symptoms: Symptom[]
  result: AssessmentResult
  timestamp: number
}

/**
 * 固定底部按钮组件 - 使用Portal渲染
 */
const FixedBottomButton: React.FC<{
  children: React.ReactNode
  show: boolean
}> = ({ children, show }) => {
  if (!show) return null

  return createPortal(
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 shadow-lg z-50">
      {children}
    </div>,
    document.body
  )
}

/**
 * 症状自评页面
 * 症状记录、分析和健康评估
 */
const SymptomAssessment: React.FC = () => {
  // 状态管理
  const [step, setStep] = useState<'select' | 'detail' | 'assessment' | 'result'>('select')
  const [selectedSymptoms, setSelectedSymptoms] = useState<Symptom[]>([])
  const [currentSymptom, setCurrentSymptom] = useState<Symptom | null>(null)
  const [assessmentResult, setAssessmentResult] = useState<AssessmentResult | null>(null)
  const [isAssessing, setIsAssessing] = useState(false)
  const [error, setError] = useState('')

  // 流式思考过程状态
  const [thoughts, setThoughts] = useState<string[]>([])
  const [currentThought, setCurrentThought] = useState('')
  const [isThinking, setIsThinking] = useState(false)
  const [thinkingCompleted, setThinkingCompleted] = useState(false)
  const [thoughtsCollapsed, setThoughtsCollapsed] = useState(false)

  // 自动滚动相关
  const assessmentEndRef = useRef<HTMLDivElement>(null)
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true)

  // 🔥 用户滚动检测 - 修复滚动问题
  useEffect(() => {
    let scrollTimer: NodeJS.Timeout
    // let isUserScrolling = false

    const handleScroll = () => {
      // 标记用户正在滚动
      // isUserScrolling = true
      setShouldAutoScroll(false)

      clearTimeout(scrollTimer)
      scrollTimer = setTimeout(() => {
        // 检查是否接近底部，如果是则恢复自动滚动
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop
        const scrollHeight = document.documentElement.scrollHeight
        const clientHeight = window.innerHeight

        if (scrollHeight - scrollTop - clientHeight < 150) {
          setShouldAutoScroll(true)
        }
        // isUserScrolling = false
      }, 1500) // 减少等待时间到1.5秒
    }

    if (step === 'assessment') {
      window.addEventListener('scroll', handleScroll, { passive: true })
    }

    return () => {
      window.removeEventListener('scroll', handleScroll)
      clearTimeout(scrollTimer)
    }
  }, [step])

  // 症状分类数据
  const symptomCategories = [
    {
      name: '头部症状',
      icon: Brain,
      symptoms: [
        { id: 'headache', name: '头痛', icon: Brain, category: 'head' },
        { id: 'dizziness', name: '头晕', icon: Brain, category: 'head' },
        { id: 'nausea', name: '恶心', icon: Brain, category: 'head' },
        { id: 'vomiting', name: '呕吐', icon: Brain, category: 'head' },
      ],
    },
    {
      name: '视听症状',
      icon: Eye,
      symptoms: [
        { id: 'blurred_vision', name: '视力模糊', icon: Eye, category: 'sensory' },
        { id: 'ear_pain', name: '耳痛', icon: Ear, category: 'sensory' },
        { id: 'hearing_loss', name: '听力下降', icon: Ear, category: 'sensory' },
        { id: 'tinnitus', name: '耳鸣', icon: Ear, category: 'sensory' },
      ],
    },
    {
      name: '呼吸系统',
      icon: Activity,
      symptoms: [
        { id: 'cough', name: '咳嗽', icon: Activity, category: 'respiratory' },
        { id: 'shortness_breath', name: '呼吸困难', icon: Activity, category: 'respiratory' },
        { id: 'chest_pain', name: '胸痛', icon: Heart, category: 'respiratory' },
        { id: 'sore_throat', name: '咽喉痛', icon: Activity, category: 'respiratory' },
      ],
    },
    {
      name: '心血管',
      icon: Heart,
      symptoms: [
        { id: 'chest_tightness', name: '胸闷', icon: Heart, category: 'cardiovascular' },
        { id: 'palpitations', name: '心悸', icon: Heart, category: 'cardiovascular' },
        { id: 'high_bp', name: '血压升高', icon: Heart, category: 'cardiovascular' },
        { id: 'fatigue', name: '乏力', icon: Heart, category: 'cardiovascular' },
      ],
    },
    {
      name: '全身症状',
      icon: Thermometer,
      symptoms: [
        { id: 'fever', name: '发热', icon: Thermometer, category: 'general' },
        { id: 'chills', name: '寒战', icon: Thermometer, category: 'general' },
        { id: 'weight_loss', name: '体重下降', icon: Shield, category: 'general' },
        { id: 'night_sweats', name: '盗汗', icon: Thermometer, category: 'general' },
      ],
    },
  ]

  // 严重程度选项
  const severityLevels = [
    { value: 1, label: '轻微', color: 'text-green-600 bg-green-50' },
    { value: 2, label: '轻度', color: 'text-yellow-600 bg-yellow-50' },
    { value: 3, label: '中度', color: 'text-orange-600 bg-orange-50' },
    { value: 4, label: '重度', color: 'text-red-600 bg-red-50' },
    { value: 5, label: '极重', color: 'text-red-700 bg-red-100' },
  ]

  // 持续时间选项
  const durationOptions = [
    '刚刚开始',
    '几小时',
    '1-2天',
    '3-7天',
    '1-2周',
    '1个月',
    '数月',
    '持续存在',
  ]

  // 频率选项
  const frequencyOptions = ['偶尔', '间歇性', '每天', '持续性', '阵发性']

  // 🔥 自动滚动监听思考过程变化
  useEffect(() => {
    if (
      step === 'assessment' &&
      shouldAutoScroll &&
      (thoughts.length > 0 || currentThought || isThinking)
    ) {
      const timer = setTimeout(() => {
        assessmentEndRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'end',
        })
      }, 200)

      return () => clearTimeout(timer)
    }
  }, [step, thoughts.length, currentThought, isThinking, shouldAutoScroll])

  // 选择症状
  const selectSymptom = (symptom: Symptom) => {
    console.log('🎯 选择症状:', symptom.name)

    // 检查是否已选择
    if (selectedSymptoms.find(s => s.id === symptom.id)) {
      return
    }

    setCurrentSymptom({ ...symptom })
    setStep('detail')
  }

  // 移除症状
  const removeSymptom = (symptomId: string) => {
    setSelectedSymptoms(prev => prev.filter(s => s.id !== symptomId))
    console.log('🗑️ 移除症状:', symptomId)
  }

  // 保存症状详情
  const saveSymptomDetail = () => {
    if (!currentSymptom) return

    setSelectedSymptoms(prev => {
      const updated = [...prev]
      const existingIndex = updated.findIndex(s => s.id === currentSymptom.id)

      if (existingIndex >= 0) {
        updated[existingIndex] = currentSymptom
      } else {
        updated.push(currentSymptom)
      }

      return updated
    })

    setCurrentSymptom(null)
    setStep('select')
    console.log('💾 保存症状详情:', currentSymptom.name)
  }

  // 开始评估
  const startAssessment = () => {
    if (selectedSymptoms.length === 0) {
      setError('请至少选择一个症状')
      return
    }

    setStep('assessment')
    performAssessment()
  }

  // 执行评估
  const performAssessment = async () => {
    if (!configUtils.isAgentConfigured('doctor')) {
      setError('症状评估服务未配置，请联系管理员')
      return
    }

    setIsAssessing(true)
    setError('')

    // 重置思考过程状态
    setThoughts([])
    setCurrentThought('')
    setIsThinking(true)
    setThinkingCompleted(false)
    setThoughtsCollapsed(false)

    try {
      console.log('🔍 开始症状评估')

      // 构建症状描述
      const symptomsDescription = selectedSymptoms
        .map(symptom => {
          return `${symptom.name}：
- 严重程度：${symptom.severity ? severityLevels.find(l => l.value === symptom.severity)?.label : '未知'}
- 持续时间：${symptom.duration || '未知'}
- 发生频率：${symptom.frequency || '未知'}
- 具体描述：${symptom.description || '无额外描述'}`
        })
        .join('\n\n')

      // 🔥 优化评估提示词，确保结构化输出
      const prompt = `作为专业的AI健康顾问，请对以下症状进行综合评估：

${symptomsDescription}

请按照以下格式提供结构化的评估报告：

## 🔍 问题核心解读：
[简要分析用户症状的核心问题和可能原因]

## 🧠 背景医学知识：
[相关的医学知识背景，帮助用户理解症状]

## ⚠️ 风险等级：**[RISK_LEVEL]**
- **low**: 症状轻微，建议观察
- **medium**: 症状中等，建议关注
- **high**: 症状较重，建议尽快就医
- **urgent**: 症状严重，建议立即就医

## 📋 详细分析：

### 1. **症状特征分析**：
- 主要症状表现
- 症状间的关联性
- 可能的病理机制

### 2. **可能原因**：
- 常见原因分析
- 需要排除的疾病
- 诱发因素

### 3. **危险信号**：
- 需要立即关注的症状
- 病情恶化的征象
- 紧急就医指征

### 4. **处理建议**：
- 即时缓解措施
- 生活方式调整
- 预防措施

### 5. **就医指导**：
- 推荐科室
- 就医时机
- 需要准备的资料

### 6. **后续关注**：
- 症状观察要点
- 复查建议
- 预后评估

**重要提示**：此评估仅供参考，不能替代专业医疗诊断。如症状严重或持续，请及时就医。

请确保内容专业、安全、实用，使用Markdown格式输出。`

      // 调用Dify API - 使用流式响应
      let fullResponse = ''
      await difyClient.sendMessageStream(config.dify.doctorAgent.appId, prompt, {
        onThought: (thought: any) => {
          console.log('💭 AI思考:', thought)
          // 🔥 修复：确保thought是字符串，防止React child错误
          const thoughtContent =
            typeof thought === 'string'
              ? thought
              : typeof thought === 'object' && thought !== null
                ? thought.thought || JSON.stringify(thought)
                : '正在思考...'
          // 🔥 检测思考完成标记
          const isThinkingCompleted = thoughtContent.includes('###THINKING_COMPLETED###')

          if (isThinkingCompleted) {
            console.log('🧠 思考过程完成，保存到历史记录')
            // 清理完成标记并保存到思考历史
            const cleanThought = thoughtContent.replace(/###THINKING_COMPLETED###/g, '').trim()
            if (cleanThought) {
              setThoughts(prev => [...prev, cleanThought])
            }
            setCurrentThought('')
            setThinkingCompleted(true)
            setIsThinking(false)
          } else {
            // 🔥 修复流式思考拼接问题：只更新当前思考，清理完成标记
            const cleanThought = thoughtContent.replace(/###THINKING_COMPLETED###/g, '').trim()
            setCurrentThought(cleanThought)
            setIsThinking(true)
          }

          // 🔥 自动滚动
          if (shouldAutoScroll) {
            setTimeout(() => {
              assessmentEndRef.current?.scrollIntoView({ behavior: 'smooth' })
            }, 100)
          }
        },
        onMessage: (content: string, isComplete: boolean) => {
          console.log('📝 收到评估内容:', { content: content.substring(0, 100), isComplete })
          fullResponse = content

          if (isComplete) {
            setIsThinking(false)
            setThinkingCompleted(true)

            // 🔥 思考完成后，将当前思考保存到数组中
            if (currentThought && currentThought.trim()) {
              setThoughts(prev => [...prev, currentThought])
            }
            setCurrentThought('')
          }

          // 🔥 自动滚动
          if (shouldAutoScroll) {
            setTimeout(() => {
              assessmentEndRef.current?.scrollIntoView({ behavior: 'smooth' })
            }, 100)
          }
        },
        onError: (error: Error) => {
          console.error('🚨 评估过程出错:', error)
          throw error
        },
        onComplete: () => {
          console.log('✅ 评估完成')
          setIsThinking(false)
          setThinkingCompleted(true)

          // 🔥 评估完成后，将当前思考保存到数组中
          if (currentThought && currentThought.trim()) {
            setThoughts(prev => [...prev, currentThought])
          }
          setCurrentThought('')
        },
      })

      // 等待流式响应完成
      while (isThinking && !thinkingCompleted) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      // 解析评估结果（简化处理，实际可以用正则或AI来解析结构化数据）
      const responseText = fullResponse || ''
      if (!responseText) {
        throw new Error('评估响应为空，请重试')
      }

      const riskLevel = responseText.toLowerCase().includes('urgent')
        ? 'urgent'
        : responseText.toLowerCase().includes('high')
          ? 'high'
          : responseText.toLowerCase().includes('medium')
            ? 'medium'
            : 'low'

      const result: AssessmentResult = {
        riskLevel,
        summary: responseText,
        suggestions: ['根据症状描述进行评估', '请仔细阅读详细分析', '如有疑问请咨询专业医生'],
        urgency:
          riskLevel === 'urgent'
            ? '建议立即就医'
            : riskLevel === 'high'
              ? '建议尽快就医'
              : riskLevel === 'medium'
                ? '建议关注症状变化'
                : '可继续观察',
        followUp: '请按照建议进行后续处理',
      }

      setAssessmentResult(result)
      setStep('result')

      // 保存评估记录
      const assessmentData: HistoryItem = {
        symptoms: selectedSymptoms,
        result,
        timestamp: Date.now(),
      }

      const history = storageUtils.local.get<HistoryItem[]>('assessment_history') || []
      history.unshift(assessmentData)
      storageUtils.local.set('assessment_history', history.slice(0, 10)) // 保留最近10条

      console.log('✅ 症状评估完成')
    } catch (error) {
      console.error('🚨 症状评估失败:', error)
      setError(error instanceof Error ? error.message : '评估失败，请稍后重试')
      setStep('select')

      // 重置思考过程状态
      setIsThinking(false)
      setThinkingCompleted(false)
      setThoughts([])
      setCurrentThought('')
    } finally {
      setIsAssessing(false)
    }
  }

  // 重新开始
  const restart = () => {
    setStep('select')
    setSelectedSymptoms([])
    setCurrentSymptom(null)
    setAssessmentResult(null)
    setError('')

    // 重置思考过程状态
    setThoughts([])
    setCurrentThought('')
    setIsThinking(false)
    setThinkingCompleted(false)
    setThoughtsCollapsed(false)
  }

  // 风险等级颜色
  const getRiskColor = (level: string) => {
    switch (level) {
      case 'urgent':
        return 'text-red-700 bg-red-100 border-red-300'
      case 'high':
        return 'text-red-600 bg-red-50 border-red-200'
      case 'medium':
        return 'text-orange-600 bg-orange-50 border-orange-200'
      case 'low':
        return 'text-green-600 bg-green-50 border-green-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  return (
    <>
      {/* 错误提示 - 顶部显示 */}
      {error && (
        <div className="fixed top-4 left-4 right-4 sm:left-1/2 sm:right-auto sm:-translate-x-1/2 sm:w-auto sm:min-w-[400px] sm:max-w-[600px] z-50">
          <div className="bg-red-50 border border-red-200 rounded-xl p-4 shadow-lg backdrop-blur-sm">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <p className="text-sm leading-relaxed text-red-700">{error}</p>
              </div>
              <button
                onClick={() => setError('')}
                className="text-red-600 hover:text-red-800 transition-colors flex-shrink-0 mt-0.5"
                title="关闭"
              >
                ✕
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="space-y-6 pt-6" style={{ paddingBottom: '120px' }}>
        {/* 步骤指示器 */}
        <div className="flex items-center justify-center space-x-4">
          {[
            { key: 'select', label: '选择症状' },
            { key: 'assessment', label: '智能评估' },
            { key: 'result', label: '评估结果' },
          ].map((stepItem, index) => (
            <div key={stepItem.key} className="flex items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step === stepItem.key
                    ? 'bg-blue-500 text-white'
                    : ['select', 'assessment', 'result'].indexOf(step) > index
                      ? 'bg-green-500 text-white'
                      : 'bg-gray-200 text-gray-600'
                }`}
              >
                {index + 1}
              </div>
              {index < 2 && <ChevronRight className="w-4 h-4 text-gray-400 mx-2" />}
            </div>
          ))}
        </div>

        <AnimatePresence mode="wait">
          {/* 症状选择 */}
          {step === 'select' && (
            <motion.div
              key="select"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              <div className="text-center">
                <h2 className="text-xl font-bold text-gray-900 mb-2">症状自评</h2>
                <p className="text-gray-600">请选择您当前的症状，我们将为您提供专业评估</p>
              </div>

              {/* 已选择的症状 */}
              {selectedSymptoms.length > 0 && (
                <div className="bg-blue-50 rounded-2xl p-4">
                  <h3 className="font-medium text-blue-900 mb-3">
                    已选择的症状 ({selectedSymptoms.length})
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedSymptoms.map(symptom => (
                      <div
                        key={symptom.id}
                        className="flex items-center space-x-2 bg-white px-3 py-2 rounded-xl border border-blue-200"
                      >
                        <span className="text-sm">{symptom.name}</span>
                        <button
                          onClick={() => removeSymptom(symptom.id)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <XCircle className="w-4 h-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 症状分类 */}
              <div className="space-y-4">
                {symptomCategories.map(category => {
                  const CategoryIcon = category.icon
                  return (
                    <div
                      key={category.name}
                      className="bg-white rounded-2xl border border-gray-200 p-4"
                    >
                      <div className="flex items-center space-x-2 mb-3">
                        <CategoryIcon className="w-5 h-5 text-blue-600" />
                        <h3 className="font-medium text-gray-900">{category.name}</h3>
                      </div>

                      <div className="grid grid-cols-2 gap-2">
                        {category.symptoms.map(symptom => {
                          const SymptomIcon = symptom.icon
                          const isSelected = selectedSymptoms.find(s => s.id === symptom.id)

                          return (
                            <button
                              key={symptom.id}
                              onClick={() => selectSymptom(symptom)}
                              disabled={!!isSelected}
                              className={`flex items-center space-x-2 p-3 rounded-xl text-sm transition-colors border ${
                                isSelected
                                  ? 'bg-green-50 text-green-700 border-green-300 shadow-sm'
                                  : 'bg-white text-gray-700 hover:bg-gray-50 border-gray-200 hover:border-gray-300 shadow-sm'
                              }`}
                            >
                              <SymptomIcon className="w-4 h-4" />
                              <span>{symptom.name}</span>
                              {isSelected && <CheckCircle className="w-4 h-4 ml-auto" />}
                            </button>
                          )
                        })}
                      </div>
                    </div>
                  )
                })}
              </div>
            </motion.div>
          )}

          {/* 症状详情 */}
          {step === 'detail' && currentSymptom && (
            <motion.div
              key="detail"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              <div className="text-center">
                <h2 className="text-xl font-bold text-gray-900 mb-2">
                  详细描述：{currentSymptom.name}
                </h2>
                <p className="text-gray-600">请详细描述症状的情况</p>
              </div>

              <div className="space-y-6">
                {/* 严重程度 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">严重程度</label>
                  <div className="grid grid-cols-5 gap-2">
                    {severityLevels.map(level => (
                      <button
                        key={level.value}
                        onClick={() =>
                          setCurrentSymptom(prev =>
                            prev ? { ...prev, severity: level.value } : null
                          )
                        }
                        className={`p-3 rounded-xl text-xs font-medium transition-colors border ${
                          currentSymptom.severity === level.value
                            ? level.color + ' border-current shadow-sm'
                            : 'bg-white text-gray-600 hover:bg-gray-50 border-gray-200 hover:border-gray-300 shadow-sm'
                        }`}
                      >
                        {level.label}
                      </button>
                    ))}
                  </div>
                </div>

                {/* 持续时间 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">持续时间</label>
                  <div className="grid grid-cols-2 gap-2">
                    {durationOptions.map(duration => (
                      <button
                        key={duration}
                        onClick={() =>
                          setCurrentSymptom(prev => (prev ? { ...prev, duration } : null))
                        }
                        className={`p-3 rounded-xl text-sm transition-colors border ${
                          currentSymptom.duration === duration
                            ? 'bg-blue-500 text-white border-blue-500 shadow-sm'
                            : 'bg-white text-gray-700 hover:bg-gray-50 border-gray-200 hover:border-gray-300 shadow-sm'
                        }`}
                      >
                        {duration}
                      </button>
                    ))}
                  </div>
                </div>

                {/* 发生频率 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">发生频率</label>
                  <div className="grid grid-cols-2 gap-2">
                    {frequencyOptions.map(frequency => (
                      <button
                        key={frequency}
                        onClick={() =>
                          setCurrentSymptom(prev => (prev ? { ...prev, frequency } : null))
                        }
                        className={`p-3 rounded-xl text-sm transition-colors border ${
                          currentSymptom.frequency === frequency
                            ? 'bg-blue-500 text-white border-blue-500 shadow-sm'
                            : 'bg-white text-gray-700 hover:bg-gray-50 border-gray-200 hover:border-gray-300 shadow-sm'
                        }`}
                      >
                        {frequency}
                      </button>
                    ))}
                  </div>
                </div>

                {/* 症状描述 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    详细描述（可选）
                  </label>
                  <textarea
                    value={currentSymptom.description || ''}
                    onChange={e =>
                      setCurrentSymptom(prev =>
                        prev ? { ...prev, description: e.target.value } : null
                      )
                    }
                    placeholder="请详细描述症状的特征、触发因素、伴随症状等..."
                    className="w-full p-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    rows={4}
                  />
                </div>
              </div>
            </motion.div>
          )}

          {/* 评估中 */}
          {step === 'assessment' && (
            <motion.div
              key="assessment"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              <div className="bg-blue-50 rounded-2xl p-8 text-center">
                <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Brain className="w-8 h-8 text-white animate-pulse" />
                </div>
                <h2 className="text-xl font-bold text-gray-900 mb-2">AI智能评估中</h2>
                <p className="text-gray-600 mb-4">正在分析您的症状，请稍候...</p>

                {/* 进度指示 */}
                <div className="space-y-3">
                  <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
                    <Clock className="w-4 h-4" />
                    <span>分析症状特征</span>
                  </div>
                  <div className="w-full bg-blue-200 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full animate-pulse"
                      style={{ width: '70%' }}
                    ></div>
                  </div>
                </div>
              </div>

              {/* 🔥 使用统一的思考过程组件 */}
              <ThoughtProcess
                thoughts={thoughts}
                currentThought={currentThought}
                isThinking={isThinking}
                collapsed={thoughtsCollapsed}
                onToggle={() => setThoughtsCollapsed(!thoughtsCollapsed)}
                className="mb-4"
              />

              {isAssessing && (
                <div className="text-center">
                  <div className="inline-flex items-center space-x-2 text-blue-600">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span className="text-sm">
                      {isThinking ? '🧠 AI正在思考分析...' : '🔄 正在生成评估报告...'}
                    </span>
                  </div>
                </div>
              )}
            </motion.div>
          )}

          {/* 评估结果 */}
          {step === 'result' && assessmentResult && (
            <motion.div
              key="result"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              <div className="text-center">
                <h2 className="text-xl font-bold text-gray-900 mb-2">评估结果</h2>
                <p className="text-gray-600">基于您的症状描述生成的专业评估</p>
              </div>

              {/* 🔥 在结果页面也显示思考过程 */}
              {thoughts.length > 0 && (
                <ThoughtProcess
                  thoughts={thoughts}
                  currentThought=""
                  isThinking={false}
                  collapsed={thoughtsCollapsed}
                  onToggle={() => setThoughtsCollapsed(!thoughtsCollapsed)}
                  className="mb-4"
                />
              )}

              {/* 风险等级 */}
              <div
                className={`p-4 rounded-2xl border-2 ${getRiskColor(assessmentResult.riskLevel)}`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-bold">风险等级</h3>
                  <div className="flex items-center space-x-2">
                    {assessmentResult.riskLevel === 'urgent' && (
                      <AlertTriangle className="w-5 h-5" />
                    )}
                    {assessmentResult.riskLevel === 'high' && <AlertTriangle className="w-5 h-5" />}
                    {assessmentResult.riskLevel === 'medium' && <Info className="w-5 h-5" />}
                    {assessmentResult.riskLevel === 'low' && <CheckCircle className="w-5 h-5" />}
                    <span className="font-medium capitalize">{assessmentResult.riskLevel}</span>
                  </div>
                </div>
                <p className="text-sm">{assessmentResult.urgency}</p>
              </div>

              {/* 详细分析 - 使用ReactMarkdown渲染 */}
              <div className="bg-white rounded-2xl border border-gray-200 p-4">
                <h3 className="font-bold text-gray-900 mb-3">详细分析</h3>
                <div className="prose prose-sm max-w-none break-words">
                  {/* 🔥 确保ReactMarkdown正常工作，提供回退方案 */}
                  {(() => {
                    try {
                      return (
                        <ReactMarkdown
                          remarkPlugins={[remarkGfm]}
                          components={{
                            h1: ({ children }: any) => (
                              <h1 className="text-base font-bold text-gray-900 mb-3 pb-2 border-b border-blue-200">
                                {children}
                              </h1>
                            ),
                            h2: ({ children }: any) => (
                              <h2 className="text-sm font-semibold text-blue-800 mb-2 mt-4">
                                {children}
                              </h2>
                            ),
                            h3: ({ children }: any) => (
                              <h3 className="text-sm font-medium text-blue-700 mb-2 mt-3">
                                {children}
                              </h3>
                            ),
                            p: ({ children }: any) => (
                              <p className="mb-3 last:mb-0 text-gray-800 leading-relaxed">
                                {children}
                              </p>
                            ),
                            strong: ({ children }: any) => (
                              <strong className="font-semibold text-blue-900">{children}</strong>
                            ),
                            em: ({ children }: any) => (
                              <em className="italic text-blue-700">{children}</em>
                            ),
                            ul: ({ children }: any) => (
                              <ul className="list-disc mb-3 space-y-0.5 ml-5 pl-1">{children}</ul>
                            ),
                            ol: ({ children }: any) => (
                              <ol className="list-decimal mb-3 space-y-0.5 ml-5 pl-1">
                                {children}
                              </ol>
                            ),
                            li: ({ children }: any) => (
                              <li className="text-gray-800 leading-normal">{children}</li>
                            ),
                          }}
                        >
                          {assessmentResult.summary}
                        </ReactMarkdown>
                      )
                    } catch (error) {
                      console.error('ReactMarkdown渲染失败，使用回退方案:', error)
                      // 简单的markdown解析回退方案
                      const processedText = assessmentResult.summary
                        .replace(
                          /\*\*(.*?)\*\*/g,
                          '<strong class="font-semibold text-blue-900">$1</strong>'
                        )
                        .replace(/\*(.*?)\*/g, '<em class="italic text-blue-700">$1</em>')
                        .replace(
                          /### (.*)/g,
                          '<h3 class="text-sm font-medium text-blue-700 mb-2 mt-3">$1</h3>'
                        )
                        .replace(
                          /## (.*)/g,
                          '<h2 class="text-sm font-semibold text-blue-800 mb-2 mt-4">$1</h2>'
                        )
                        .replace(
                          /# (.*)/g,
                          '<h1 class="text-base font-bold text-gray-900 mb-3 pb-2 border-b border-blue-200">$1</h1>'
                        )
                        .replace(
                          /\n\n/g,
                          '</p><p class="mb-3 last:mb-0 text-gray-800 leading-relaxed">'
                        )
                        .replace(/\n/g, '<br>')
                        .replace(/(\d+\.\s)/g, '<br><strong>$1</strong>')
                        .replace(/-\s/g, '<br>• ')

                      return (
                        <div
                          className="text-gray-800 leading-relaxed"
                          dangerouslySetInnerHTML={{
                            __html: `<p class="mb-3 last:mb-0 text-gray-800 leading-relaxed">${processedText}</p>`,
                          }}
                        />
                      )
                    }
                  })()}
                </div>
              </div>

              {/* 后续建议 */}
              <div className="bg-yellow-50 rounded-2xl border border-yellow-200 p-4">
                <h3 className="font-bold text-yellow-900 mb-3">后续建议</h3>
                <p className="text-sm text-yellow-800">{assessmentResult.followUp}</p>
              </div>

              {/* 免责声明 */}
              <div className="bg-gray-50 rounded-2xl p-4">
                <div className="flex items-start space-x-2 text-sm text-gray-600">
                  <Info className="w-4 h-4 mt-0.5 flex-shrink-0" />
                  <p>
                    此评估结果仅供参考，不能替代专业医疗诊断。如症状严重或持续，请及时就医咨询专业医生。
                  </p>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 🔥 自动滚动锚点 */}
        <div ref={assessmentEndRef} className="h-1" />
      </div>

      {/* 🔥 使用Portal渲染的固定按钮 - 症状选择页面 */}
      <FixedBottomButton show={selectedSymptoms.length > 0 && step === 'select'}>
        <button
          onClick={startAssessment}
          className="w-full btn-primary flex items-center justify-center"
        >
          <Send className="w-5 h-5 mr-2" />
          开始智能评估 ({selectedSymptoms.length} 个症状)
        </button>
      </FixedBottomButton>

      {/* 🔥 使用Portal渲染的固定按钮 - 症状详情页面 */}
      <FixedBottomButton show={step === 'detail' && currentSymptom !== null}>
        <div className="flex space-x-3">
          <button
            onClick={() => setStep('select')}
            className="flex-1 btn-secondary flex items-center justify-center"
          >
            <ChevronLeft className="w-5 h-5 mr-2" />
            返回
          </button>
          <button
            onClick={saveSymptomDetail}
            className="flex-1 btn-primary flex items-center justify-center"
          >
            保存症状
            <ChevronRight className="w-5 h-5 ml-2" />
          </button>
        </div>
      </FixedBottomButton>

      {/* 🔥 使用Portal渲染的固定按钮 - 评估结果页面 */}
      <FixedBottomButton show={step === 'result' && assessmentResult !== null}>
        <div className="space-y-3">
          <button onClick={restart} className="w-full btn-primary flex items-center justify-center">
            重新评估
          </button>
          <button
            onClick={() => setStep('select')}
            className="w-full btn-secondary flex items-center justify-center"
          >
            返回症状选择
          </button>
        </div>
      </FixedBottomButton>
    </>
  )
}

export default SymptomAssessment
