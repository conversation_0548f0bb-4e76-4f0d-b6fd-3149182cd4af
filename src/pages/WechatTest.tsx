/**
 * 微信功能测试页面
 * 用于测试微信JS-SDK配置和功能
 */
import React, { useEffect, useState } from 'react'
import { useWechatConfigContext } from '../components/WechatConfigProvider'
import { initWechatWithDefaultShare, setWechatShare } from '../utils/wechatShare'
import { getCurrentUrlForWechatConfig, getDeviceInfo } from '../utils/networkUtils'

const WechatTest: React.FC = () => {
  const {
    status,
    isReady,
    isLoading,
    error,
    config,
    isInWechat,
    initConfig,
    resetConfig,
    retryConfig
  } = useWechatConfigContext()

  const [testResults, setTestResults] = useState<string[]>([])
  const [isTestingShare, setIsTestingShare] = useState(false)

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`])
  }

  // 测试微信分享功能
  const testWechatShare = async () => {
    setIsTestingShare(true)
    addTestResult('开始测试微信分享功能...')

    try {
      await initWechatWithDefaultShare()
      addTestResult('✅ 微信分享功能初始化成功')

      // 设置自定义分享内容
      setWechatShare({
        title: '健康助手测试页面',
        desc: '这是一个微信JS-SDK功能测试页面',
        link: window.location.href,
        imgUrl: `${window.location.origin}/favicon.ico`
      })
      addTestResult('✅ 自定义分享内容设置成功')

    } catch (error: any) {
      addTestResult(`❌ 微信分享功能测试失败: ${error.message}`)
    } finally {
      setIsTestingShare(false)
    }
  }

  // 测试微信位置功能
  const testWechatLocation = () => {
    if (!window.wx) {
      addTestResult('❌ 微信JS-SDK未加载')
      return
    }

    addTestResult('开始测试微信位置功能...')
    
    window.wx.getLocation({
      type: 'wgs84',
      success: (res: any) => {
        addTestResult(`✅ 位置获取成功: 纬度${res.latitude}, 经度${res.longitude}`)
      },
      fail: (err: any) => {
        addTestResult(`❌ 位置获取失败: ${err.errMsg}`)
      }
    })
  }

  // 测试微信扫码功能
  const testWechatScan = () => {
    if (!window.wx) {
      addTestResult('❌ 微信JS-SDK未加载')
      return
    }

    addTestResult('开始测试微信扫码功能...')
    
    window.wx.scanQRCode({
      needResult: 1,
      scanType: ["qrCode", "barCode"],
      success: (res: any) => {
        addTestResult(`✅ 扫码成功: ${res.resultStr}`)
      },
      fail: (err: any) => {
        addTestResult(`❌ 扫码失败: ${err.errMsg}`)
      }
    })
  }

  // 清空测试结果
  const clearTestResults = () => {
    setTestResults([])
  }

  const deviceInfo = getDeviceInfo()
  const currentUrl = getCurrentUrlForWechatConfig()

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* 页面标题 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            微信功能测试页面
          </h1>
          <p className="text-gray-600">
            用于测试微信JS-SDK配置和各项功能是否正常工作
          </p>
        </div>

        {/* 微信配置状态 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            微信配置状态
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium">微信环境:</span>
                <span className={`text-sm ${isInWechat ? 'text-green-600' : 'text-red-600'}`}>
                  {isInWechat ? '✅ 是' : '❌ 否'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-sm font-medium">配置状态:</span>
                <span className={`text-sm ${
                  status === 'success' ? 'text-green-600' : 
                  status === 'error' ? 'text-red-600' : 
                  status === 'loading' ? 'text-blue-600' : 'text-gray-600'
                }`}>
                  {status === 'success' && '✅ 成功'}
                  {status === 'error' && '❌ 失败'}
                  {status === 'loading' && '⏳ 加载中'}
                  {status === 'idle' && '⚪ 待初始化'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-sm font-medium">配置就绪:</span>
                <span className={`text-sm ${isReady ? 'text-green-600' : 'text-gray-600'}`}>
                  {isReady ? '✅ 是' : '❌ 否'}
                </span>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium">平台:</span>
                <span className="text-sm text-gray-600">
                  {deviceInfo.isIOS ? 'iOS' : deviceInfo.isAndroid ? 'Android' : '其他'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-sm font-medium">微信版本:</span>
                <span className="text-sm text-gray-600">
                  {deviceInfo.wechatVersion || 'N/A'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-sm font-medium">AppId:</span>
                <span className="text-sm text-gray-600 font-mono">
                  {config?.config?.appId || 'N/A'}
                </span>
              </div>
            </div>
          </div>

          {error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded text-sm text-red-600">
              <strong>错误信息:</strong> {error}
            </div>
          )}

          {/* 配置操作按钮 */}
          <div className="mt-4 flex gap-2">
            <button
              onClick={() => initConfig(true)}
              disabled={isLoading}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 text-sm"
            >
              {isLoading ? '配置中...' : '重新配置'}
            </button>
            
            <button
              onClick={resetConfig}
              disabled={isLoading}
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 disabled:opacity-50 text-sm"
            >
              重置配置
            </button>
            
            {error && (
              <button
                onClick={() => retryConfig()}
                disabled={isLoading}
                className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50 text-sm"
              >
                重试
              </button>
            )}
          </div>
        </div>

        {/* 功能测试 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            功能测试
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <button
              onClick={testWechatShare}
              disabled={!isReady || isTestingShare}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 text-sm"
            >
              {isTestingShare ? '测试中...' : '测试分享功能'}
            </button>
            
            <button
              onClick={testWechatLocation}
              disabled={!isReady}
              className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50 text-sm"
            >
              测试位置功能
            </button>
            
            <button
              onClick={testWechatScan}
              disabled={!isReady}
              className="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600 disabled:opacity-50 text-sm"
            >
              测试扫码功能
            </button>
          </div>

          <div className="flex justify-between items-center mb-2">
            <h3 className="text-md font-medium text-gray-900">测试结果</h3>
            <button
              onClick={clearTestResults}
              className="px-3 py-1 bg-gray-400 text-white rounded hover:bg-gray-500 text-xs"
            >
              清空
            </button>
          </div>
          
          <div className="bg-gray-900 text-green-400 p-4 rounded font-mono text-xs max-h-64 overflow-y-auto">
            {testResults.length === 0 ? (
              <div className="text-gray-500">暂无测试结果...</div>
            ) : (
              testResults.map((result, index) => (
                <div key={index} className="mb-1">
                  {result}
                </div>
              ))
            )}
          </div>
        </div>

        {/* 调试信息 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            调试信息
          </h2>
          
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">当前URL:</h3>
              <div className="bg-gray-100 p-2 rounded text-xs font-mono break-all">
                {window.location.href}
              </div>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">签名URL:</h3>
              <div className="bg-gray-100 p-2 rounded text-xs font-mono break-all">
                {currentUrl}
              </div>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">初始URL:</h3>
              <div className="bg-gray-100 p-2 rounded text-xs font-mono break-all">
                {(window as any).initUrl || 'N/A'}
              </div>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">User Agent:</h3>
              <div className="bg-gray-100 p-2 rounded text-xs font-mono break-all">
                {navigator.userAgent}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default WechatTest
