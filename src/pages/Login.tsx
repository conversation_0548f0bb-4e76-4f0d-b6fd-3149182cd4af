/**
 * 登录页面
 * 支持短信验证码登录和微信授权登录
 * 参考华润项目Login.tsx和MyLogin.tsx的完整实现
 */

import React, { useState, useEffect, useRef } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { motion } from 'framer-motion'
import { useAuth, AuthStatus } from '../hooks/useAuth'
import { SMSType } from '../services/authService'
import { validatePhoneNumber } from '../utils/encryption'
import {
  isFromWeiXin,
  getQueryVariableValue,
  getPlatform,
  getAppPlatform,
  isNativeApp,
  getDeviceInfo,
} from '../utils/networkUtils'
import { initiateWeChatAuth, WeChatScope } from '../services/wechatAuthService'
import Toast from '../components/Toast'
import { useToast } from '../hooks/useToast'

interface LoginState {
  mobile: string
  smsCode: string
  agreeChecked: boolean
}

const Login: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const inputRef = useRef<HTMLInputElement>(null)
  const { toast, showError, showSuccess, hideToast } = useToast()

  const {
    authStatus,
    isLoading,
    error,
    login,
    signup,
    bindPhone,
    sendSMSCode,
    clearError,
    wxCode,
  } = useAuth()

  const [formData, setFormData] = useState<LoginState>({
    mobile: '',
    smsCode: '',
    agreeChecked: false,
  })

  const [countdown, setCountdown] = useState(0)
  const [loginMode, setLoginMode] = useState<'login' | 'signup' | 'bind'>('login')

  // 倒计时效果
  useEffect(() => {
    let timer: NodeJS.Timeout
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000)
    }
    return () => clearTimeout(timer)
  }, [countdown])

  // 检查微信Code确定登录模式
  useEffect(() => {
    const urlWxCode = getQueryVariableValue('code')
    if (urlWxCode || wxCode) {
      setLoginMode('bind') // 有微信授权码说明需要绑定手机号
    } else if (isFromWeiXin()) {
      setLoginMode('signup') // 在微信环境但没有授权码
    } else {
      setLoginMode('login') // 非微信环境，使用手机号登录
    }
  }, [wxCode])

  // 监听认证状态变化
  useEffect(() => {
    if (authStatus === AuthStatus.AUTHENTICATED) {
      console.log('✅ 登录成功，准备跳转')
      const from = location.state?.from || '/'
      navigate(from, { replace: true })
    }
  }, [authStatus, navigate, location.state])

  // 清除错误信息
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        clearError()
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [error, clearError])

  // 输入框变化处理
  const handleInputChange = (field: keyof LoginState, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (error) clearError()
  }

  // 发送验证码
  const handleSendCode = async () => {
    if (!formData.mobile) {
      showError('请输入手机号')
      return
    }

    if (!validatePhoneNumber(formData.mobile)) {
      showError('手机号格式不正确')
      return
    }

    // 根据模式选择短信类型
    let smsType = SMSType.LOGIN
    if (loginMode === 'bind' || loginMode === 'signup') {
      smsType = SMSType.BIND
    }

    const success = await sendSMSCode(formData.mobile, smsType)
    if (success) {
      setCountdown(60)
      showSuccess('验证码已发送')
      // 聚焦到验证码输入框
      setTimeout(() => {
        inputRef.current?.focus()
      }, 100)
    }
  }

  // 提交登录
  const handleSubmit = async () => {
    if (!formData.mobile) {
      showError('请输入手机号')
      return
    }

    if (!validatePhoneNumber(formData.mobile)) {
      showError('手机号格式不正确')
      return
    }

    if (!formData.smsCode) {
      showError('请输入验证码')
      return
    }

    if (!formData.agreeChecked) {
      showError('请同意用户协议')
      return
    }

    let success = false

    try {
      switch (loginMode) {
        case 'bind':
          // 绑定手机号到微信账号
          success = await bindPhone(formData.mobile, formData.smsCode)
          if (!success) {
            // 绑定失败，尝试注册
            success = await signup(formData.mobile, formData.smsCode)
          }
          break
        case 'signup':
          // 注册新用户
          success = await signup(formData.mobile, formData.smsCode)
          break
        case 'login':
        default:
          // 手机号登录
          success = await login(formData.mobile, formData.smsCode)
          break
      }

      if (success) {
        console.log('✅ 登录/注册成功')
      }
    } catch (error) {
      console.error('登录/注册失败:', error)
    }
  }

  // 微信授权登录
  const handleWeChatLogin = async () => {
    if (!isFromWeiXin()) {
      showError('请在微信中打开')
      return
    }

    try {
      await initiateWeChatAuth(WeChatScope.USERINFO)
    } catch (error: any) {
      console.error('微信授权失败:', error)
      showError(error.message || '微信授权失败')
    }
  }

  // 获取页面标题和按钮文本
  const getPageInfo = () => {
    switch (loginMode) {
      case 'bind':
        return {
          title: '绑定手机号',
          subtitle: '请绑定手机号以完成登录',
          buttonText: '绑定并登录',
          description: '绑定手机号后，您可以在微信中快速登录',
        }
      case 'signup':
        return {
          title: '注册账号',
          subtitle: '欢迎使用健康助手',
          buttonText: '注册并登录',
          description: '注册即表示您同意我们的服务条款',
        }
      case 'login':
      default:
        return {
          title: '手机号登录',
          subtitle: '欢迎回来',
          buttonText: '登录',
          description: '使用手机号和验证码登录您的账号',
        }
    }
  }

  const pageInfo = getPageInfo()

  return (
    <>
      <Toast
        message={toast.message}
        type={toast.type}
        visible={toast.visible}
        onClose={hideToast}
      />

      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, ease: 'easeOut' }}
          className="max-w-md w-full"
        >
          {/* 应用图标和标题 */}
          <motion.div
            className="text-center mb-8"
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.1, duration: 0.4 }}
          >
            <div className="mx-auto w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg">
              <svg
                className="w-10 h-10 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                />
              </svg>
            </div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
              健康助手
            </h1>
            <p className="text-gray-600">{pageInfo.subtitle}</p>
          </motion.div>

          {/* 表单 */}
          <motion.div
            className="bg-white rounded-2xl shadow-lg p-6"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2, duration: 0.4 }}
          >
            {/* 错误提示 */}
            {error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
                {error}
              </div>
            )}

            {/* 微信授权按钮（仅在微信环境且没有微信code时显示） */}
            {isFromWeiXin() && !wxCode && loginMode !== 'bind' && (
              <div className="mb-6">
                <button
                  onClick={handleWeChatLogin}
                  className="w-full py-3 bg-green-500 text-white rounded-lg font-medium hover:bg-green-600 flex items-center justify-center"
                >
                  <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.096 4.203 2.895 5.52.149.109.469.448.359.895-.219.895-.716 2.736-.815 3.078-.109.359-.515.465-.219.258.359-.258 2.053-1.384 2.891-1.971.515-.359.859-.219 1.203-.109 1.205.328 2.516.547 3.876.547 4.8 0 8.691-3.288 8.691-7.342C18.881 5.476 14.99 2.188 8.691 2.188zm-.297 9.172c-.297.297-.766.109-.859-.297-.094-.406.281-.875.578-.594.297.282.578.594.281.891zm3.422 0c-.297.297-.766.109-.859-.297-.094-.406.281-.875.578-.594.297.282.578.594.281.891z" />
                  </svg>
                  微信授权登录
                </button>

                <div className="mt-4 text-center">
                  <span className="text-gray-400 text-sm">或</span>
                </div>
              </div>
            )}

            {/* 手机号输入 */}
            <motion.div
              className="mb-4"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3, duration: 0.3 }}
            >
              <label className="block text-sm font-medium text-gray-700 mb-2">手机号</label>
              <input
                type="tel"
                value={formData.mobile}
                onChange={e => handleInputChange('mobile', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 hover:border-blue-400"
                placeholder="请输入手机号"
                maxLength={11}
              />
            </motion.div>

            {/* 验证码输入 */}
            <motion.div
              className="mb-6"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4, duration: 0.3 }}
            >
              <label className="block text-sm font-medium text-gray-700 mb-2">验证码</label>
              <div className="flex gap-3">
                <input
                  ref={inputRef}
                  type="text"
                  value={formData.smsCode}
                  onChange={e => handleInputChange('smsCode', e.target.value)}
                  className="flex-1 px-2 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 hover:border-blue-400"
                  placeholder="请输入验证码"
                  maxLength={6}
                />
                <motion.button
                  onClick={handleSendCode}
                  disabled={countdown > 0 || !formData.mobile || isLoading}
                  className={`px-4 py-3 rounded-lg text-sm font-medium whitespace-nowrap transition-all duration-200 ${
                    countdown > 0 || !formData.mobile || isLoading
                      ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                      : 'bg-blue-600 text-white hover:bg-blue-700 hover:shadow-md'
                  }`}
                  whileHover={
                    countdown === 0 && formData.mobile && !isLoading ? { scale: 1.05 } : {}
                  }
                  whileTap={countdown === 0 && formData.mobile && !isLoading ? { scale: 0.95 } : {}}
                >
                  {countdown > 0 ? `${countdown}s` : '获取验证码'}
                </motion.button>
              </div>
            </motion.div>

            {/* 协议同意 */}
            <motion.div
              className="mb-6"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.3 }}
            >
              <label className="flex items-start">
                <input
                  type="checkbox"
                  checked={formData.agreeChecked}
                  onChange={e => handleInputChange('agreeChecked', e.target.checked)}
                  className="mt-1 mr-2 accent-blue-600"
                />
                <span className="text-sm text-gray-600">
                  我已阅读并同意
                  <a
                    href="/common-doc/?docpath=agreement&readonly=true"
                    className="text-blue-600 hover:underline mx-1 transition-colors duration-200 hover:text-blue-800"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    《用户协议》
                  </a>
                </span>
              </label>
            </motion.div>

            {/* 登录按钮 */}
            <motion.button
              onClick={handleSubmit}
              disabled={
                isLoading || !formData.mobile || !formData.smsCode || !formData.agreeChecked
              }
              className={`w-full py-3 rounded-lg font-medium transition-all duration-200 ${
                isLoading || !formData.mobile || !formData.smsCode || !formData.agreeChecked
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 hover:shadow-lg'
              }`}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.3 }}
              whileHover={
                !isLoading && formData.mobile && formData.smsCode && formData.agreeChecked
                  ? { scale: 1.02 }
                  : {}
              }
              whileTap={
                !isLoading && formData.mobile && formData.smsCode && formData.agreeChecked
                  ? { scale: 0.98 }
                  : {}
              }
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  处理中...
                </div>
              ) : (
                pageInfo.buttonText
              )}
            </motion.button>
          </motion.div>

          {/* 底部说明 */}
          <motion.div
            className="text-center mt-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8, duration: 0.3 }}
          >
            <p className="text-sm text-gray-500">{pageInfo.description}</p>
          </motion.div>

          {/* 调试信息（开发环境） */}
          {import.meta.env.DEV && (
            <div className="mt-4 p-3 bg-gray-100 rounded-lg text-xs text-gray-600">
              <div className="font-bold mb-2">🐛 Debug Info:</div>
              <div>登录模式: {loginMode}</div>
              <div>认证状态: {authStatus}</div>
              <div>微信环境: {isFromWeiXin() ? '是' : '否'}</div>
              <div>微信Code: {wxCode || '无'}</div>
              <div>加载中: {isLoading ? '是' : '否'}</div>
              <div>平台: {getPlatform()}</div>
              <div>APP平台: {getAppPlatform()}</div>
              <div>原生APP: {isNativeApp() ? '是' : '否'}</div>
              <div>设备信息: {JSON.stringify(getDeviceInfo(), null, 2)}</div>
            </div>
          )}
        </motion.div>
      </div>
    </>
  )
}

export default Login
