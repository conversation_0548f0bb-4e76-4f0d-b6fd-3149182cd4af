/**
 * 网络配置和URL解析工具
 * 参考华润项目的网络配置实现
 */

// 获取医院代码 - 从URL参数或路径中解析
export const getHospitalCode = (): string => {
  // 开发环境直接使用配置
  if (import.meta.env.DEV && window.AppConfig?.hospCode) {
    return window.AppConfig.hospCode
  }

  let hospitalCode = ''

  // 先从查询参数中查找
  const query = window.location.search.substring(1)
  const vars = query.split('&')
  for (let i = 0; i < vars.length; i++) {
    const pair = vars[i].split('=')
    if (decodeURIComponent(pair[0]) === 'hospital') {
      hospitalCode = decodeURIComponent(pair[1])
      break
    }
  }

  // 如果查询参数中没有，尝试从路径中解析
  if (!hospitalCode) {
    const reg = /hospcode-(.*)/
    const match = window.location.pathname.match(reg)
    if (match) {
      hospitalCode = match[1].split('/')[0]
    }
  }

  console.log('hospitalCode==', hospitalCode)
  return hospitalCode
}

/**
 * 获取地址栏里某个参数的值
 * @param paramName 参数名称
 * @returns 参数值
 */
export function getQueryVariableValue(paramName: string): string {
  const query = window.location.search.substring(1)
  const vars = query.split('&')
  for (let i = 0; i < vars.length; i++) {
    const pair = vars[i].split('=')
    if (decodeURIComponent(pair[0]) === paramName) {
      return decodeURIComponent(pair[1])
    }
  }
  return ''
}

// 设置微信公众平台AppId
const setMPAppId = (appId?: string) => {
  if (appId) {
    const localAppId = localStorage.getItem(getHospitalCode())
    if (localAppId && localAppId !== appId) {
      // 如果本地已经有了appId并且和url不一致 清除登录状态，重新发起授权
      console.log('本地已经有了appId并且和url不一致 清除登录状态，重新发起授权')
      clearLocalToken()
      localStorage.setItem(getHospitalCode(), appId)
    } else if (localAppId && localAppId === appId) {
      // 本地有，但和上次一样，不管是否登录，不处理
      console.log('本地有，但和上次一样，不管是否登录，不处理')
      localStorage.setItem(getHospitalCode(), appId)
    } else if (!localAppId && getToken()) {
      // 本地没有appId，但已经登录了，来了一个新的，说明上次登录就是用的默认公众号，清除登录状态，重新发起授权
      console.log(
        '本地没有缓存appId，是登录状态，来了一个新的，说明上次登录就是用的默认公众号，清除登录状态，重新发起授权'
      )
      clearLocalToken()
      localStorage.setItem(getHospitalCode(), appId)
    } else {
      console.log('本地没有缓存appId，且非登录状态，但这次url里有appId，把本次的存入缓存即可')
      localStorage.setItem(getHospitalCode(), appId)
    }
  } else {
    console.log('Url里有没有appId，本地没有缓存appId，且非登录状态，把本次的appId存入缓存即可')
  }
}

// 获取微信公众平台id
export const getMPAppId = (): string | null => {
  const hospitalCode = getHospitalCode()
  return hospitalCode ? localStorage.getItem(hospitalCode) : null
}

// Token管理 - 参考华润项目的完整实现
const TOKEN_KEY = 'ih_wx_token'
const REFRESH_TOKEN_KEY = 'ih_refresh_token'
const TOKEN_EXPIRE_KEY = 'ih_token_expire'
const USER_INFO_KEY = 'ih_user_info'

// JWT token解析
export const parseJWTPayload = (token: string): any => {
  try {
    // JWT格式：header.payload.signature
    const parts = token.split('.')
    if (parts.length !== 3) {
      throw new Error('Invalid JWT format')
    }

    // 解码payload部分
    const payload = parts[1]
    const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'))
    return JSON.parse(decoded)
  } catch (error) {
    console.error('解析JWT token失败:', error)
    throw error
  }
}

// 从JWT token中提取用户信息
export const getUserInfoFromToken = (token?: string): any | null => {
  try {
    const actualToken = token || getToken()
    if (!actualToken) {
      return null
    }

    const payload = parseJWTPayload(actualToken)
    return {
      userId: payload.sub || payload.userId || payload.id,
      username: payload.username || payload.name,
      roles: payload.roles || payload.authorities || [],
      exp: payload.exp,
      iat: payload.iat,
      ...payload,
    }
  } catch (error) {
    console.error('从token中提取用户信息失败:', error)
    return null
  }
}

// 获取JWT Token
export const getToken = (): string => {
  const token = localStorage.getItem(TOKEN_KEY)
  return token ? token : ''
}

// 设置 JWT Token
export const setToken = (token: string, expiresIn?: number): boolean => {
  if (!token) {
    console.warn('试图设置空的token')
    return false
  }

  try {
    localStorage.setItem(TOKEN_KEY, token)

    // 设置过期时间（如果提供）
    if (expiresIn) {
      const expireTime = Date.now() + expiresIn * 1000
      localStorage.setItem(TOKEN_EXPIRE_KEY, expireTime.toString())
    }

    console.log('🔑 Token已存储')
    return true
  } catch (error) {
    console.error('存储Token失败:', error)
    return false
  }
}

// 获取刷新Token
export const getRefreshToken = (): string => {
  const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY)
  return refreshToken ? refreshToken : ''
}

// 设置刷新Token
export const setRefreshToken = (refreshToken: string): boolean => {
  if (!refreshToken) {
    return false
  }

  try {
    localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken)
    return true
  } catch (error) {
    console.error('存储RefreshToken失败:', error)
    return false
  }
}

// 检查Token是否过期
export const isTokenExpired = (): boolean => {
  const token = getToken()
  if (!token) {
    return true
  }

  const expireTimeStr = localStorage.getItem(TOKEN_EXPIRE_KEY)
  if (!expireTimeStr) {
    // 没有过期时间信息，尝试解析JWT token
    try {
      const payload = parseJWTPayload(token)
      if (payload.exp) {
        const expireTime = payload.exp * 1000 // JWT时间戳是秒，需要转换为毫秒
        const now = Date.now()
        return now >= expireTime
      }
    } catch (error) {
      console.warn('解析JWT token失败，假设token有效:', error)
    }
    return false // 无法确定过期时间，假设没过期
  }

  const expireTime = parseInt(expireTimeStr, 10)
  const now = Date.now()
  const isExpired = now >= expireTime

  if (isExpired) {
    console.log('🕒 Token已过期:', {
      expireTime: new Date(expireTime).toLocaleString(),
      currentTime: new Date(now).toLocaleString(),
    })
  }

  return isExpired
}

// 获取Token过期时间
export const getTokenExpireTime = (): number | null => {
  const expireTimeStr = localStorage.getItem(TOKEN_EXPIRE_KEY)
  return expireTimeStr ? parseInt(expireTimeStr, 10) : null
}

// 获取缓存的用户信息
export const getCachedUserInfo = (): any | null => {
  try {
    const userInfoStr = localStorage.getItem(USER_INFO_KEY)
    return userInfoStr ? JSON.parse(userInfoStr) : null
  } catch (error) {
    console.error('解析用户信息失败:', error)
    return null
  }
}

// 缓存用户信息
export const setCachedUserInfo = (userInfo: any): boolean => {
  try {
    localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo))
    return true
  } catch (error) {
    console.error('缓存用户信息失败:', error)
    return false
  }
}

// 清除所有认证相关的本地存储
export const clearLocalToken = (): void => {
  try {
    localStorage.removeItem(TOKEN_KEY)
    localStorage.removeItem(REFRESH_TOKEN_KEY)
    localStorage.removeItem(TOKEN_EXPIRE_KEY)
    localStorage.removeItem(USER_INFO_KEY)
    console.log('🗑️ 已清除所有认证相关的本地存储')
  } catch (error) {
    console.error('清除本地存储失败:', error)
  }
}

// Token自动刷新机制
export const shouldRefreshToken = (): boolean => {
  const token = getToken()
  if (!token) {
    return false
  }

  const expireTimeStr = localStorage.getItem(TOKEN_EXPIRE_KEY)
  if (!expireTimeStr) {
    try {
      const payload = parseJWTPayload(token)
      if (payload.exp) {
        const expireTime = payload.exp * 1000
        const now = Date.now()
        const timeUntilExpire = expireTime - now
        // 如果token在5分钟内过期，则需要刷新
        return timeUntilExpire <= 5 * 60 * 1000
      }
    } catch (error) {
      console.warn('检查token刷新需求时出错:', error)
    }
    return false
  }

  const expireTime = parseInt(expireTimeStr, 10)
  const now = Date.now()
  const timeUntilExpire = expireTime - now

  // 如果token在5分钟内过期，则需要刷新
  return timeUntilExpire <= 5 * 60 * 1000 && timeUntilExpire > 0
}

// 验证token有效性
export const validateToken = (): boolean => {
  const token = getToken()
  if (!token) {
    console.log('🚫 没有token')
    return false
  }

  if (isTokenExpired()) {
    console.log('🚫 Token已过期')
    clearLocalToken()
    return false
  }

  try {
    const userInfo = getUserInfoFromToken(token)
    if (!userInfo || !userInfo.userId) {
      console.log('🚫 Token中没有有效用户信息')
      clearLocalToken()
      return false
    }

    console.log('✅ Token验证通过')
    return true
  } catch (error) {
    console.error('🚫 Token验证失败:', error)
    clearLocalToken()
    return false
  }
}

// 获取存储信息统计
export const getStorageInfo = () => {
  const token = getToken()
  const userInfo = getUserInfoFromToken(token)

  return {
    hasToken: !!token,
    hasRefreshToken: !!getRefreshToken(),
    hasUserInfo: !!getCachedUserInfo(),
    tokenExpired: isTokenExpired(),
    shouldRefresh: shouldRefreshToken(),
    expireTime: getTokenExpireTime(),
    tokenUserInfo: userInfo,
    storageKeys: {
      token: TOKEN_KEY,
      refreshToken: REFRESH_TOKEN_KEY,
      expire: TOKEN_EXPIRE_KEY,
      userInfo: USER_INFO_KEY,
    },
  }
}

// 获取基础URL
export const getBaseUrl = (): string => {
  let baseUrl = ''
  console.log('myBaseUrl==', window.AppConfig?.myBaseUrl)
  try {
    baseUrl = window.AppConfig?.myBaseUrl || 'https://api.example.com'
    return baseUrl
  } catch (error) {
    console.error('baseUrl wrong = ', error, window.AppConfig?.myBaseUrl)
    return 'https://api.example.com'
  }
}

// 初始化微信AppId
const appId = getQueryVariableValue('appId')
console.log('公众平台appId==', appId)
setMPAppId(appId)

// 检测是否在微信浏览器中
export const isFromWeiXin = (): boolean => {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('micromessenger')
}

// 检测是否在移动端
export const isMobile = (): boolean => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

// 检测是否在iOS设备
export const isIOS = (): boolean => {
  const ua = navigator.userAgent.toLowerCase()
  return /iphone|ipad|ipod/.test(ua)
}

// 检测是否在Android设备
export const isAndroid = (): boolean => {
  const ua = navigator.userAgent.toLowerCase()
  return /android/.test(ua)
}

/**
 * 获取移动端操作系统类型
 * @returns 1: Android, 2: iOS, 0: 其他
 */
export const getMobileOS = (): number => {
  const ua = navigator.userAgent
  const isAndroidDevice = ua.indexOf('Android') > -1 || ua.indexOf('Adr') > -1
  const isIOSDevice = !!ua.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)

  if (isAndroidDevice) {
    return 1 // Android
  } else if (isIOSDevice) {
    return 2 // iOS
  }
  return 0 // 其他
}

/**
 * 获取微信版本号
 * @returns 微信版本号字符串
 */
export const getWeChatVersion = (): string | undefined => {
  const weChatInfo = navigator.userAgent.match(/MicroMessenger\/([\d\\.]+)/i)
  if (weChatInfo) {
    return weChatInfo[1]
  }
  return undefined
}

/**
 * 检查微信版本是否支持某些功能
 * @returns 是否支持高级功能
 */
export const isWeChatVersionSupported = (): boolean => {
  const version = getWeChatVersion()
  if (!version) return false

  const versionParts = version.split('.')
  const majorVersion = parseInt(versionParts[0], 10)
  const minorVersion = parseInt(versionParts[1], 10)
  const patchVersion = parseInt(versionParts[2], 10)

  // 支持微信版本 7.0.12 及以上
  if (majorVersion > 7) return true
  if (majorVersion === 7) {
    if (minorVersion > 0) return true
    if (minorVersion === 0 && patchVersion >= 12) return true
  }

  return false
}

// 获取平台类型
export const getPlatform = (): 'ios' | 'android' | 'web' | 'wechat' => {
  if (isFromWeiXin()) {
    return 'wechat'
  }
  if (isIOS()) {
    return 'ios'
  }
  if (isAndroid()) {
    return 'android'
  }
  return 'web'
}

// 检测是否为移动APP环境（通过userAgent判断）
export const isNativeApp = (): boolean => {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('healthassistant') || ua.includes('nativeapp')
}

// 检测具体的APP平台
export const getAppPlatform = (): 'ios_app' | 'android_app' | 'web' => {
  if (!isNativeApp()) {
    return 'web'
  }

  if (isIOS()) {
    return 'ios_app'
  }

  if (isAndroid()) {
    return 'android_app'
  }

  return 'web'
}

// 检测是否在微信小程序中
export const isWeChatMiniProgram = (): boolean => {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('miniprogram')
}

// 获取设备信息
export const getDeviceInfo = () => {
  const ua = navigator.userAgent
  return {
    isMobile: isMobile(),
    isIOS: isIOS(),
    isAndroid: isAndroid(),
    isFromWeiXin: isFromWeiXin(),
    isWeChatMiniProgram: isWeChatMiniProgram(),
    isNativeApp: isNativeApp(),
    platform: getPlatform(),
    appPlatform: getAppPlatform(),
    userAgent: ua,
    navigatorPlatform: navigator.platform,
    language: navigator.language,
    cookieEnabled: navigator.cookieEnabled,
    onLine: navigator.onLine,
    // 微信相关信息
    mobileOS: getMobileOS(),
    wechatVersion: getWeChatVersion(),
    isWechatVersionSupported: isWeChatVersionSupported(),
    // 屏幕信息
    screenWidth: window.screen.width,
    screenHeight: window.screen.height,
    windowWidth: window.innerWidth,
    windowHeight: window.innerHeight,
    devicePixelRatio: window.devicePixelRatio || 1
  }
}

/**
 * 获取当前页面URL用于微信JS-SDK签名
 * iOS和Android平台处理方式不同
 * @returns 用于签名的URL
 */
export const getCurrentUrlForWechatConfig = (): string => {
  let url: string = window.location.href

  // iOS系统使用初始URL，Android使用当前URL
  if (getMobileOS() === 2) {
    // iOS系统，使用页面初始加载时的URL
    url = (window as any).initUrl || window.location.href
  }

  // 移除hash部分，微信签名不包含hash
  if (url) {
    url = url.split('#')[0]
  }

  console.log('🔗 微信配置URL:', {
    platform: getMobileOS() === 2 ? 'iOS' : getMobileOS() === 1 ? 'Android' : 'Other',
    originalUrl: window.location.href,
    initUrl: (window as any).initUrl,
    finalUrl: url
  })

  return url
}

/**
 * 初始化页面URL（在页面首次加载时调用）
 * 主要用于iOS平台的微信签名
 */
export const initPageUrl = (): void => {
  if (!((window as any).initUrl)) {
    (window as any).initUrl = window.location.href
    console.log('📍 初始化页面URL:', (window as any).initUrl)
  }
}



// 获取微信公众号授权URL
export const getWeChatAuthUrl = (
  appId?: string,
  redirectUrl?: string,
  scope: string = 'snsapi_userinfo'
): string => {
  const finalAppId = appId || getMPAppId() || window.AppConfig?.WECHAT_CONFIG?.APP_ID || ''
  const finalRedirectUrl = redirectUrl || window.location.href
  const state = 'health_assistant_' + Date.now()

  if (!finalAppId) {
    console.error('未配置微信AppId')
    return ''
  }

  return `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${finalAppId}&redirect_uri=${encodeURIComponent(finalRedirectUrl)}&response_type=code&scope=${scope}&state=${state}#wechat_redirect`
}

// 检测网络连接状态
export const getNetworkStatus = (): 'online' | 'offline' | 'unknown' => {
  if (typeof navigator.onLine !== 'undefined') {
    return navigator.onLine ? 'online' : 'offline'
  }
  return 'unknown'
}

// 获取页面可见性状态
export const getPageVisibility = (): 'visible' | 'hidden' | 'prerender' | 'unloaded' => {
  if (typeof document.visibilityState !== 'undefined') {
    return document.visibilityState as 'visible' | 'hidden' | 'prerender' | 'unloaded'
  }
  return 'visible'
}

// 全局类型声明已在 src/types/global.d.ts 中定义
