import wx from 'weixin-js-sdk'

// 全局类型定义在 src/types/global.d.ts 中

// 微信分享配置接口
interface WechatShareConfig {
  title: string // 分享标题
  desc: string // 分享描述
  link: string // 分享链接
  imgUrl: string // 分享图标
}

// 微信JS-SDK配置接口
interface WechatJSConfig {
  appId: string
  timestamp: number
  nonceStr: string
  signature: string
}

/**
 * 获取微信配置
 */
const getWechatConfig = () => {
  return (
    window.AppConfig?.WECHAT_CONFIG || {
      APP_ID: 'your_wechat_appid',
      SHARE_DEFAULT: {
        title: 'AI健康助理',
        desc: '您的专业健康顾问',
        imgUrl: '/health-icon.svg',
      },
      SIGNATURE_API: '/api/wechat/signature',
      ENABLED: true,
      DEBUG: false,
    }
  )
}

/**
 * 初始化微信JS-SDK
 * @param config 微信配置参数
 */
export const initWechatJS = (config: WechatJSConfig): Promise<void> => {
  const wechatConfig = getWechatConfig()

  return new Promise((resolve, reject) => {
    wx.config({
      debug: wechatConfig.DEBUG, // 使用配置中的调试模式
      appId: config.appId,
      timestamp: config.timestamp,
      nonceStr: config.nonceStr,
      signature: config.signature,
      jsApiList: [
        'updateAppMessageShareData', // 分享给朋友
        'updateTimelineShareData', // 分享到朋友圈
        'onMenuShareAppMessage', // 旧版分享给朋友接口（兼容）
        'onMenuShareTimeline', // 旧版分享到朋友圈接口（兼容）
      ],
    })

    wx.ready(() => {
      console.log('🔥 微信JS-SDK初始化成功')
      resolve()
    })

    wx.error((res: any) => {
      console.error('❌ 微信JS-SDK初始化失败:', res)
      reject(res)
    })
  })
}

/**
 * 设置微信分享内容
 * @param shareConfig 分享配置
 */
export const setWechatShare = (shareConfig: WechatShareConfig) => {
  console.log('🔗 设置微信分享内容:', shareConfig)

  // 新版分享接口
  wx.updateAppMessageShareData({
    title: shareConfig.title,
    desc: shareConfig.desc,
    link: shareConfig.link,
    imgUrl: shareConfig.imgUrl,
    success: () => {
      console.log('✅ 分享给朋友配置成功')
    },
    fail: (err: any) => {
      console.error('❌ 分享给朋友配置失败:', err)
    },
  })

  wx.updateTimelineShareData({
    title: shareConfig.title,
    link: shareConfig.link,
    imgUrl: shareConfig.imgUrl,
    success: () => {
      console.log('✅ 分享到朋友圈配置成功')
    },
    fail: (err: any) => {
      console.error('❌ 分享到朋友圈配置失败:', err)
    },
  })

  // 旧版接口兼容（用于老版本微信）
  wx.onMenuShareAppMessage({
    title: shareConfig.title,
    desc: shareConfig.desc,
    link: shareConfig.link,
    imgUrl: shareConfig.imgUrl,
    success: () => {
      console.log('✅ 旧版分享给朋友成功')
    },
    cancel: () => {
      console.log('🚫 用户取消分享给朋友')
    },
  })

  wx.onMenuShareTimeline({
    title: shareConfig.title,
    link: shareConfig.link,
    imgUrl: shareConfig.imgUrl,
    success: () => {
      console.log('✅ 旧版分享到朋友圈成功')
    },
    cancel: () => {
      console.log('🚫 用户取消分享到朋友圈')
    },
  })
}

/**
 * 检查是否在微信浏览器中
 */
export const isWechatBrowser = (): boolean => {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('micromessenger')
}

/**
 * 获取微信签名配置（需要后端API支持）
 * @param url 当前页面URL
 */
export const getWechatSignature = async (url: string): Promise<WechatJSConfig> => {
  const wechatConfig = getWechatConfig()

  // 检查是否启用微信分享功能
  if (!wechatConfig.ENABLED) {
    console.log('🚫 微信分享功能已禁用')
    throw new Error('微信分享功能已禁用')
  }

  try {
    console.log('🔐 正在获取微信签名，URL:', url)

    // 🔥 调用后端API获取微信签名
    const signatureApi = wechatConfig.SIGNATURE_API
    if (!signatureApi) {
      throw new Error('微信签名API未配置')
    }

    const response = await fetch(signatureApi, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url }),
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    console.log('✅ 微信签名获取成功:', {
      appId: data.appId,
      timestamp: data.timestamp,
      nonceStr: data.nonceStr,
      signature: data.signature ? '***' : 'empty', // 不打印完整签名
    })

    return {
      appId: data.appId || wechatConfig.APP_ID,
      timestamp: data.timestamp,
      nonceStr: data.nonceStr,
      signature: data.signature,
    }
  } catch (error) {
    console.error('❌ 获取微信签名失败:', error)

    // 🔥 开发环境返回模拟配置
    if (wechatConfig.DEBUG) {
      console.log('🧪 调试模式：返回模拟微信配置')
      return {
        appId: wechatConfig.APP_ID,
        timestamp: Math.floor(Date.now() / 1000),
        nonceStr: Math.random().toString(36).substring(2),
        signature: 'debug_signature',
      }
    }

    throw error
  }
}

/**
 * 默认分享配置
 */
export const getDefaultShareConfig = (): WechatShareConfig => {
  const wechatConfig = getWechatConfig()
  const currentUrl = window.location.href
  const shareDefault = wechatConfig.SHARE_DEFAULT
  const currentTitle = document.title || shareDefault?.title || '健康助手'

  return {
    title: currentTitle,
    desc: shareDefault?.desc || '智能健康分析，专业医疗咨询',
    link: currentUrl,
    imgUrl: shareDefault?.imgUrl
      ? `${window.location.origin}${shareDefault.imgUrl}`
      : `${window.location.origin}/favicon.ico`,
  }
}
