import { wechatConfigService } from '../services/wechatConfigService'
import { isFromWeiXin } from './networkUtils'

// 全局类型定义在 src/types/global.d.ts 中

// 微信分享配置接口
interface WechatShareConfig {
  title: string // 分享标题
  desc: string // 分享描述
  link: string // 分享链接
  imgUrl: string // 分享图标
}

// 微信JS-SDK配置接口（保持向后兼容）
interface WechatJSConfig {
  appId: string
  timestamp: number
  nonceStr: string
  signature: string
}

/**
 * 获取微信配置
 */
const getWechatConfig = () => {
  return (
    window.AppConfig?.WECHAT_CONFIG || {
      APP_ID: 'your_wechat_appid',
      SHARE_DEFAULT: {
        title: 'AI健康助理',
        desc: '您的专业健康顾问',
        imgUrl: '/health-icon.svg',
      },
      SIGNATURE_API: '/api/wechat/signature',
      ENABLED: true,
      DEBUG: false,
    }
  )
}

/**
 * 等待微信JS-SDK加载
 */
const waitForWeChatSDK = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    // 如果已经加载，直接返回
    if (typeof window.wx !== 'undefined') {
      resolve()
      return
    }

    // 等待加载，最多等待10秒
    let attempts = 0
    const maxAttempts = 100 // 10秒，每100ms检查一次

    const checkSDK = () => {
      attempts++

      if (typeof window.wx !== 'undefined') {
        console.log('✅ [wechatShare] 微信JS-SDK加载完成')
        resolve()
        return
      }

      if (attempts >= maxAttempts) {
        reject(new Error('微信JS-SDK加载超时，请检查网络连接'))
        return
      }

      setTimeout(checkSDK, 100)
    }

    console.log('⏳ [wechatShare] 等待微信JS-SDK加载...')
    checkSDK()
  })
}

/**
 * 初始化微信JS-SDK（兼容旧版本）
 * 现在使用统一的微信配置服务
 * @param config 微信配置参数（可选，如果不提供则使用统一配置服务）
 */
export const initWechatJS = async (config?: WechatJSConfig): Promise<void> => {
  console.log('🔧 [initWechatJS] 开始初始化微信JS-SDK')

  // 检查微信环境
  if (!isFromWeiXin()) {
    console.warn('🚫 [initWechatJS] 不在微信环境中')
    throw new Error('不在微信环境中')
  }

  try {
    // 如果没有提供配置，使用统一的微信配置服务
    if (!config) {
      console.log('🔄 [initWechatJS] 使用统一微信配置服务')
      const result = await wechatConfigService.initWeChatConfig()
      
      if (!result.isReady) {
        throw new Error(result.error || '微信配置失败')
      }
      
      console.log('✅ [initWechatJS] 统一微信配置服务初始化成功')
      return
    }

    // 使用提供的配置（向后兼容）
    console.log('🔄 [initWechatJS] 使用提供的配置初始化')
    const wechatConfig = getWechatConfig()

    // 等待微信JS-SDK加载
    await waitForWeChatSDK()

    return new Promise((resolve, reject) => {
      window.wx.config({
        debug: wechatConfig.DEBUG, // 使用配置中的调试模式
        appId: config.appId,
        timestamp: config.timestamp,
        nonceStr: config.nonceStr,
        signature: config.signature,
        jsApiList: [
          'onMenuShareAppMessage', // 旧版分享给朋友接口（兼容）
          'onMenuShareTimeline', // 旧版分享到朋友圈接口（兼容）
          'chooseImage',
          'previewImage',
          'uploadImage',
          'downloadImage',
          'startRecord',
          'stopRecord',
          'uploadVoice',
          'downloadVoice',
          'playVoice',
          'pauseVoice',
          'stopVoice',
          'translateVoice',
          'openLocation',
          'getLocation',
          'scanQRCode',
          'chooseWXPay'
        ],
      })

      window.wx.ready(() => {
        console.log('✅ [initWechatJS] 微信JS-SDK初始化成功')
        resolve()
      })

      window.wx.error((res: any) => {
        console.error('❌ [initWechatJS] 微信JS-SDK初始化失败:', res)
        reject(res)
      })
    })
  } catch (error) {
    console.error('❌ [initWechatJS] 初始化过程出错:', error)
    throw error
  }
}

/**
 * 设置微信分享内容
 * @param shareConfig 分享配置
 */
export const setWechatShare = async (shareConfig: WechatShareConfig) => {
  console.log('🔗 设置微信分享内容:', shareConfig)

  // 检查微信环境和SDK状态
  if (!isFromWeiXin()) {
    console.warn('🚫 不在微信环境中，无法设置分享')
    return
  }

  try {
    // 等待微信JS-SDK加载
    await waitForWeChatSDK()
  } catch (error) {
    console.warn('🚫 微信JS-SDK加载失败，无法设置分享:', error)
    return
  }


  // 旧版接口兼容（用于老版本微信）
  window.wx.onMenuShareAppMessage({
    title: shareConfig.title,
    desc: shareConfig.desc,
    link: shareConfig.link,
    imgUrl: shareConfig.imgUrl,
    success: () => {
      console.log('✅ 旧版分享给朋友成功')
    },
    cancel: () => {
      console.log('🚫 用户取消分享给朋友')
    },
  })

  window.wx.onMenuShareTimeline({
    title: shareConfig.title,
    link: shareConfig.link,
    imgUrl: shareConfig.imgUrl,
    success: () => {
      console.log('✅ 旧版分享到朋友圈成功')
    },
    cancel: () => {
      console.log('🚫 用户取消分享到朋友圈')
    },
  })
}

/**
 * 检查是否在微信浏览器中
 */
export const isWechatBrowser = (): boolean => {
  return isFromWeiXin()
}

/**
 * 获取微信签名配置（兼容旧版本，现在推荐使用统一配置服务）
 * @param url 当前页面URL
 */
export const getWechatSignature = async (url?: string): Promise<WechatJSConfig> => {
  console.log('🔐 [getWechatSignature] 获取微信签名配置')
  
  const wechatConfig = getWechatConfig()

  // 检查是否启用微信分享功能
  if (!wechatConfig.ENABLED) {
    console.log('🚫 微信分享功能已禁用')
    throw new Error('微信分享功能已禁用')
  }

  try {
    // 使用统一的微信配置服务
    const result = await wechatConfigService.initWeChatConfig()
    
    if (!result.isReady || !result.config) {
      throw new Error(result.error || '微信配置失败')
    }

    console.log('✅ [getWechatSignature] 通过统一配置服务获取签名成功')
    
    return {
      appId: result.config.appId,
      timestamp: result.config.timestamp,
      nonceStr: result.config.nonceStr,
      signature: result.config.signature,
    }
  } catch (error) {
    console.error('❌ [getWechatSignature] 获取微信签名失败:', error)

    // 🔥 开发环境返回模拟配置
    if (wechatConfig.DEBUG || import.meta.env.DEV) {
      console.log('🧪 调试模式：返回模拟微信配置')
      return {
        appId: wechatConfig.APP_ID,
        timestamp: Math.floor(Date.now() / 1000),
        nonceStr: Math.random().toString(36).substring(2),
        signature: 'debug_signature',
      }
    }

    throw error
  }
}

/**
 * 默认分享配置
 */
export const getDefaultShareConfig = (): WechatShareConfig => {
  const wechatConfig = getWechatConfig()
  const currentUrl = window.location.href
  const shareDefault = wechatConfig.SHARE_DEFAULT
  const currentTitle = document.title || shareDefault?.title || '健康助手'

  return {
    title: currentTitle,
    desc: shareDefault?.desc || '智能健康分析，专业医疗咨询',
    link: currentUrl,
    imgUrl: shareDefault?.imgUrl
      ? `${window.location.origin}${shareDefault.imgUrl}`
      : `${window.location.origin}/favicon.ico`,
  }
}

/**
 * 便捷方法：初始化微信并设置默认分享
 */
export const initWechatWithDefaultShare = async (): Promise<void> => {
  try {
    // 初始化微信JS-SDK
    await initWechatJS()
    
    // 设置默认分享配置
    const shareConfig = getDefaultShareConfig()
    setWechatShare(shareConfig)
    
    console.log('✅ 微信初始化和分享配置完成')
  } catch (error) {
    console.error('❌ 微信初始化和分享配置失败:', error)
    throw error
  }
}

// 导出类型
export type { WechatJSConfig, WechatShareConfig }
