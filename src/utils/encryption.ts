/**
 * 加密工具函数
 * 用于敏感数据的加密，如手机号
 */

/**
 * 简单的AES加密实现（生产环境应使用更安全的加密库）
 * 这里使用Base64编码作为简化实现
 * @param text 要加密的文本
 * @returns 加密后的字符串
 */
export const aesEncrypt = (text: string): string => {
  try {
    // 生产环境应该使用真正的AES加密
    // 这里使用Base64编码作为占位符
    return btoa(unescape(encodeURIComponent(text)))
  } catch (error) {
    console.error('加密失败:', error)
    return text // 加密失败时返回原文
  }
}

/**
 * 简单的AES解密实现
 * @param encryptedText 加密的文本
 * @returns 解密后的字符串
 */
export const aesDecrypt = (encryptedText: string): string => {
  try {
    return decodeURIComponent(escape(atob(encryptedText)))
  } catch (error) {
    console.error('解密失败:', error)
    return encryptedText // 解密失败时返回原文
  }
}

/**
 * 验证手机号格式
 * @param phone 手机号
 * @returns 是否为有效手机号
 */
export const validatePhoneNumber = (phone: string): boolean => {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 手机号脱敏显示
 * @param phone 手机号
 * @returns 脱敏后的手机号（如：138****1234）
 */
export const maskPhoneNumber = (phone: string): string => {
  if (!phone || phone.length !== 11) {
    return phone
  }
  return `${phone.substring(0, 3)}****${phone.substring(7)}`
}
