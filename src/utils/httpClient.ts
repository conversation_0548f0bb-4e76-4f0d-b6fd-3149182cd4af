/**
 * HTTP客户端配置
 * 参考华润项目的网络请求封装
 */
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios'
import {
  getBaseUrl,
  getHospitalCode,
  getMPAppId,
  getToken,
  setToken,
  clearLocalToken,
} from './networkUtils'

// 创建axios实例配置
const getAxiosOption = (): AxiosRequestConfig => {
  console.log('getAxiosOption=====', getBaseUrl())
  const config: AxiosRequestConfig = {
    timeout: 40000,
    baseURL: getBaseUrl(),
  }

  // 设置请求头
  config.headers = {}

  // 添加医院代码到请求头
  if (getHospitalCode()) {
    config.headers['IH_HOSPITAL'] = getHospitalCode()
  }

  // 添加微信AppId到请求头
  if (getMPAppId()) {
    config.headers['IH_APPID'] = getMPAppId()
    console.log('IH_APPID', config.headers['IH_APPID'])
  }

  return config
}

// 创建axios实例
export const httpClient = axios.create(getAxiosOption())

// 请求拦截器 - 参考华润项目NetUtil.ts的完整实现
httpClient.interceptors.request.use(
  config => {
    // 添加认证头
    const token = getToken()
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }

    // 获取统一的参数 - 参考华润项目，既通过header也通过params传递医院代码
    const commonParams = {
      hospital: getHospitalCode(),
    }

    // 如果原始请求中已有参数，则合并它们；否则，只使用commonParams
    config.params = {
      ...commonParams,
      ...config.params,
    }

    // 处理URL末尾的斜杠
    if (config.url && config.url.endsWith('/')) {
      config.url = config.url.slice(0, config.url.length - 1)
    }

    return config
  },
  error => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器 - 统一错误处理
httpClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // 检查响应中是否包含新的token（如刷新token场景）
    const newToken = response.headers['new-token'] || response.data?.token
    if (newToken) {
      console.log('🔄 检测到新token，更新本地存储')
      setToken(newToken)
    }

    return response
  },
  error => {
    console.error('HTTP请求错误:', error)

    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 401:
          // 未授权，清除token并处理登录逻辑
          console.log('🚫 认证失败，清除本地token')
          clearLocalToken()

          if (data?.code === 'error.jwt_token_expired') {
            console.log('🔄 Token已过期，触发重新登录')
            window.dispatchEvent(new CustomEvent('auth:tokenExpired'))
          } else if (data?.code === 'error.wechat_user_not_found') {
            console.log('🔄 微信用户不存在，需要绑定手机号')
            window.dispatchEvent(new CustomEvent('auth:needPhoneBinding'))
          } else {
            console.log('🔄 认证失败，需要重新登录')
            window.dispatchEvent(new CustomEvent('auth:authRequired'))
          }
          break
        case 400:
          console.log('❌ 请求参数错误:', data)
          if (data?.code === 'error.wechat_user_not_found') {
            window.dispatchEvent(new CustomEvent('auth:needPhoneBinding'))
          }
          break
        case 404:
          console.log('❌ 请求的资源不存在:', error.config.url)
          break
        case 500:
          console.log('❌ 服务器内部错误')
          break
        default:
          console.log('❌ 其他HTTP错误:', status, data)
      }
    } else if (error.request) {
      console.log('❌ 网络请求失败:', error.request)
    } else {
      console.log('❌ 请求配置错误:', error.message)
    }

    return Promise.reject(error)
  }
)

// 默认请求配置
const defaultConfig = {
  method: 'get',
  responseType: 'json' as const,
}

// 封装的请求函数
export const request = <T = any>(config: AxiosRequestConfig): Promise<T> => {
  return httpClient({ ...defaultConfig, ...config }).then(response => response.data)
}

// 便捷方法
export const http = {
  get: <T = any>(url: string, params?: any, config?: AxiosRequestConfig): Promise<T> =>
    request<T>({ method: 'GET', url, params, ...config }),

  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    request<T>({ method: 'POST', url, data, ...config }),

  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    request<T>({ method: 'PUT', url, data, ...config }),

  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    request<T>({ method: 'PATCH', url, data, ...config }),

  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> =>
    request<T>({ method: 'DELETE', url, ...config }),
}

export default httpClient
