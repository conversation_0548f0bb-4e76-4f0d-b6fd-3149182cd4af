// 全局类型定义
declare global {
  interface Window {
    AppConfig?: {
      // Dify API 配置
      baseURL?: string
      apiKey?: string
      reportAppId?: string
      doctorAppId?: string
      VITE_APP_VERSION?: string

      // 网络配置 - 参考华润项目
      myBaseUrl?: string
      hospCode?: string

      // 微信分享配置
      WECHAT_CONFIG?: {
        APP_ID: string
        SHARE_DEFAULT?: {
          title: string
          desc: string
          imgUrl: string
        }
        SIGNATURE_API?: string
        ENABLED?: boolean
        DEBUG?: boolean
        [key: string]: any
      }
      [key: string]: any
    }
    wx?: any
  }
}

export {}
