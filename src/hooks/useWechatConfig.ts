/**
 * 微信配置Hook
 * 管理微信JS-SDK配置状态和生命周期
 */
import { useState, useEffect, useCallback, useRef } from 'react'
import { 
  wechatConfigService, 
  WeChatConfigResult, 
  WeChatConfigStatus,
  WeChatConfigEventListener 
} from '../services/wechatConfigService'
import { isFromWeiXin } from '../utils/networkUtils'

// Hook选项接口
export interface UseWechatConfigOptions {
  // 是否自动初始化配置
  autoInit?: boolean
  // 是否在路由变化时重新配置
  refreshOnRouteChange?: boolean
  // 是否启用调试日志
  enableDebugLog?: boolean
}

// Hook返回值接口
export interface UseWechatConfigReturn {
  // 配置状态
  status: WeChatConfigStatus
  // 是否已准备就绪
  isReady: boolean
  // 是否正在加载
  isLoading: boolean
  // 错误信息
  error: string | null
  // 配置结果
  config: WeChatConfigResult | null
  // 手动初始化配置
  initConfig: (forceRefresh?: boolean) => Promise<WeChatConfigResult>
  // 重置配置
  resetConfig: () => void
  // 重试配置
  retryConfig: () => Promise<WeChatConfigResult>
}

/**
 * 微信配置Hook
 * @param options 配置选项
 * @returns 微信配置状态和操作方法
 */
export const useWechatConfig = (options: UseWechatConfigOptions = {}): UseWechatConfigReturn => {
  const {
    autoInit = true,
    refreshOnRouteChange = true,
    enableDebugLog = import.meta.env.DEV
  } = options

  // 状态管理
  const [status, setStatus] = useState<WeChatConfigStatus>(WeChatConfigStatus.IDLE)
  const [isReady, setIsReady] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [config, setConfig] = useState<WeChatConfigResult | null>(null)

  // 引用管理
  const isInitializedRef = useRef(false)
  const currentPathRef = useRef(window.location.pathname)

  // 调试日志
  const debugLog = useCallback((message: string, ...args: any[]) => {
    if (enableDebugLog) {
      console.log(`🔧 [useWechatConfig] ${message}`, ...args)
    }
  }, [enableDebugLog])

  // 更新状态
  const updateState = useCallback((result: WeChatConfigResult) => {
    setStatus(result.status)
    setIsReady(result.isReady)
    setError(result.error || null)
    setConfig(result)
    
    debugLog('状态更新:', {
      status: result.status,
      isReady: result.isReady,
      error: result.error,
      url: result.url
    })
  }, [debugLog])

  // 初始化配置
  const initConfig = useCallback(async (forceRefresh: boolean = false): Promise<WeChatConfigResult> => {
    debugLog('开始初始化微信配置', { forceRefresh })

    // 检查微信环境
    if (!isFromWeiXin()) {
      const result: WeChatConfigResult = {
        status: WeChatConfigStatus.ERROR,
        isReady: false,
        error: '不在微信环境中'
      }
      updateState(result)
      return result
    }

    try {
      const result = await wechatConfigService.initWeChatConfig(forceRefresh)
      updateState(result)
      return result
    } catch (error: any) {
      const result: WeChatConfigResult = {
        status: WeChatConfigStatus.ERROR,
        isReady: false,
        error: error.message || '配置失败'
      }
      updateState(result)
      return result
    }
  }, [updateState, debugLog])

  // 重置配置
  const resetConfig = useCallback(() => {
    debugLog('重置微信配置')
    wechatConfigService.reset()
    setStatus(WeChatConfigStatus.IDLE)
    setIsReady(false)
    setError(null)
    setConfig(null)
    isInitializedRef.current = false
  }, [debugLog])

  // 重试配置
  const retryConfig = useCallback(async (): Promise<WeChatConfigResult> => {
    debugLog('重试微信配置')
    return initConfig(true)
  }, [initConfig, debugLog])

  // 事件监听器
  const onConfigReady: WeChatConfigEventListener = useCallback((result) => {
    debugLog('配置就绪事件', result)
    updateState(result)
  }, [updateState, debugLog])

  const onConfigError: WeChatConfigEventListener = useCallback((result) => {
    debugLog('配置错误事件', result)
    updateState(result)
  }, [updateState, debugLog])

  const onConfigRetry: WeChatConfigEventListener = useCallback((result) => {
    debugLog('配置重试事件', result)
    updateState(result)
  }, [updateState, debugLog])

  // 路由变化检测
  useEffect(() => {
    if (!refreshOnRouteChange) return

    const currentPath = window.location.pathname
    if (currentPathRef.current !== currentPath && isInitializedRef.current) {
      debugLog('检测到路由变化，重新配置微信', {
        from: currentPathRef.current,
        to: currentPath
      })
      
      // 延迟重新配置，确保页面已完全加载
      setTimeout(() => {
        initConfig(true)
      }, 500)
    }
    currentPathRef.current = currentPath
  }, [refreshOnRouteChange, initConfig, debugLog])

  // 组件挂载时的初始化
  useEffect(() => {
    // 注册事件监听器
    wechatConfigService.on('ready', onConfigReady)
    wechatConfigService.on('error', onConfigError)
    wechatConfigService.on('retry', onConfigRetry)

    // 自动初始化
    if (autoInit && !isInitializedRef.current) {
      debugLog('自动初始化微信配置')
      isInitializedRef.current = true
      
      // 延迟初始化，确保页面已完全加载
      setTimeout(() => {
        initConfig()
      }, 100)
    }

    // 清理函数
    return () => {
      wechatConfigService.off('ready', onConfigReady)
      wechatConfigService.off('error', onConfigError)
      wechatConfigService.off('retry', onConfigRetry)
    }
  }, [autoInit, initConfig, onConfigReady, onConfigError, onConfigRetry, debugLog])

  // 页面可见性变化时重新配置
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && isReady) {
        debugLog('页面重新可见，检查微信配置状态')
        // 检查当前配置是否仍然有效
        const currentStatus = wechatConfigService.getCurrentStatus()
        if (!currentStatus || currentStatus.status !== WeChatConfigStatus.SUCCESS) {
          debugLog('微信配置状态异常，重新配置')
          initConfig(true)
        }
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [isReady, initConfig, debugLog])

  return {
    status,
    isReady,
    isLoading: status === WeChatConfigStatus.LOADING,
    error,
    config,
    initConfig,
    resetConfig,
    retryConfig
  }
}

// 导出默认配置的Hook
export const useWechatConfigDefault = () => useWechatConfig()

// 导出仅手动控制的Hook
export const useWechatConfigManual = () => useWechatConfig({ 
  autoInit: false, 
  refreshOnRouteChange: false 
})
