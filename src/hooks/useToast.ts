/**
 * Toast Hook
 * 全局Toast管理
 */
import { useState, useCallback } from 'react'
import type { ToastType } from '../components/Toast'

interface ToastState {
  message: string
  type: ToastType
  visible: boolean
}

export const useToast = () => {
  const [toast, setToast] = useState<ToastState>({
    message: '',
    type: 'info',
    visible: false,
  })

  const showToast = useCallback((message: string, type: ToastType = 'info') => {
    setToast({
      message,
      type,
      visible: true,
    })
  }, [])

  const showSuccess = useCallback(
    (message: string) => {
      showToast(message, 'success')
    },
    [showToast]
  )

  const showError = useCallback(
    (message: string) => {
      showToast(message, 'error')
    },
    [showToast]
  )

  const showWarning = useCallback(
    (message: string) => {
      showToast(message, 'warning')
    },
    [showToast]
  )

  const showInfo = useCallback(
    (message: string) => {
      showToast(message, 'info')
    },
    [showToast]
  )

  const hideToast = useCallback(() => {
    setToast(prev => ({ ...prev, visible: false }))
  }, [])

  return {
    toast,
    showToast,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    hideToast,
  }
}
