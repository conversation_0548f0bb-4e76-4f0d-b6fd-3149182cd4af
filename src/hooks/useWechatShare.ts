import { useEffect, useState } from 'react'
import {
  getDefaultShareConfig,
  getWechatSignature,
  initWechatJS,
  isWechatBrowser,
  setWechatShare,
} from '../utils/wechatShare'

interface UseWechatShareOptions {
  title?: string
  desc?: string
  imgUrl?: string
}

/**
 * 微信分享Hook
 * @param options 分享配置选项
 */
export const useWechatShare = (options?: UseWechatShareOptions) => {
  const [isWechat, setIsWechat] = useState(false)
  const [shareReady, setShareReady] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // 检查是否在微信浏览器中
    const inWechat = isWechatBrowser()
    setIsWechat(inWechat)

    if (!inWechat) {
      console.log('📱 当前不在微信浏览器中，跳过微信分享配置')
      return
    }

    console.log('🔥 检测到微信浏览器，开始配置微信分享')
    initWechatShare()
  }, [])

  /**
   * 初始化微信分享
   */
  const initWechatShare = async () => {
    try {
      // 获取当前页面URL（不包含hash）
      const currentUrl = window.location.href.split('#')[0]

      console.log('🔗 获取微信签名，当前URL:', currentUrl)

      // 获取微信签名配置
      const config = await getWechatSignature(currentUrl)

      // 初始化微信JS-SDK
      await initWechatJS(config)

      // 设置分享内容
      updateShareContent()

      setShareReady(true)
      setError(null)
    } catch (err) {
      console.error('❌ 初始化微信分享失败:', err)
      setError('微信分享配置失败')
      setShareReady(false)
    }
  }

  /**
   * 更新分享内容
   */
  const updateShareContent = (customOptions?: UseWechatShareOptions) => {
    if (!isWechat) return

    const defaultConfig = getDefaultShareConfig()
    const finalOptions = { ...options, ...customOptions }

    const shareConfig = {
      title: finalOptions?.title || defaultConfig.title,
      desc: finalOptions?.desc || defaultConfig.desc,
      link: defaultConfig.link,
      imgUrl: finalOptions?.imgUrl || defaultConfig.imgUrl,
    }

    console.log('🔄 更新微信分享内容:', shareConfig)
    setWechatShare(shareConfig)
  }

  /**
   * 手动刷新分享配置（用于页面路由变化时）
   */
  const refreshShare = () => {
    if (isWechat && shareReady) {
      updateShareContent()
    }
  }

  return {
    isWechat, // 是否在微信浏览器中
    shareReady, // 分享功能是否就绪
    error, // 错误信息
    updateShareContent, // 更新分享内容
    refreshShare, // 刷新分享配置
  }
}
