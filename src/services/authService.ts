/**
 * 认证服务
 * 参考华润项目的微信授权和手机号绑定流程
 */
import { http } from '../utils/httpClient'
import { getQueryVariableValue, isFromWeiXin } from '../utils/networkUtils'

// API端点常量 - 参考华润项目ApiConstants
export const AUTH_APIS = {
  // 微信授权登录
  WECHAT_LOGIN: '/wechat/login',
  // 短信验证码登录
  SMS_LOGIN: '/login/sms',
  // 短信验证码注册
  SMS_SIGNUP: '/signup',
  // 绑定微信和手机号
  WECHAT_BIND: '/wechat/bind',
  // 获取登录短信验证码
  LOGIN_SMS_CODE: '/sms/login',
  // 获取微信注册短信验证码
  WECHAT_SMS_CODE: '/sms/wechat',
  // 获取微信JS配置
  WECHAT_JS_CONFIG: '/wechat/mp/jsconfig',
  // 获取微信AppId
  WECHAT_APP_ID: '/wechat/appid',
  // 退出登录
  LOGOUT: '/logout',
}

// 短信验证码类型
export enum SMSType {
  LOGIN = 'LOGIN',
  SIGNUP = 'SIGNUP',
  BIND = 'BIND',
}

// 用户信息接口
export interface UserInfo {
  id: string
  mobile?: string
  nickname?: string
  avatar?: string
  wechatOpenId?: string
  isFirstLogin?: boolean
}

// 登录响应接口
export interface LoginResponse {
  idToken: string
  user: UserInfo
  isNewUser?: boolean
}

// 微信JS配置接口
export interface WeChatJSConfig {
  appId: string
  timestamp: number
  nonceStr: string
  signature: string
  jsApiList: string[]
}

/**
 * 获取短信验证码 - 参考华润项目SMSService
 * @param mobile 手机号
 * @param type 验证码类型
 */
export const getSMSCode = async (mobile: string, type: SMSType = SMSType.LOGIN): Promise<void> => {
  let url = ''

  // 根据类型选择不同的接口，参考华润项目逻辑
  switch (type) {
    case SMSType.LOGIN:
      url = AUTH_APIS.LOGIN_SMS_CODE // /sms/login
      break
    case SMSType.BIND:
    case SMSType.SIGNUP:
      url = AUTH_APIS.WECHAT_SMS_CODE // /sms/wechat
      break
    default:
      url = AUTH_APIS.LOGIN_SMS_CODE
      break
  }

  // 使用GET方法，参数放在params中，参考华润项目
  return http.get(url, { mobile })
}

/**
 * 手机号验证码登录
 * @param mobile 手机号（加密后）
 * @param code 验证码
 * @param signupSource 注册来源
 */
export const loginBySMSCode = async (
  mobile: string,
  code: string,
  signupSource: string = 'H5_PATIENT'
): Promise<LoginResponse> => {
  // 参考ih-h5项目LoginService.ts中的logiBySMSCode方法
  return http.post(AUTH_APIS.SMS_LOGIN, {
    mobile,
    smsCode: code,
    rememberMe: true,
    signupSource,
  })
}

/**
 * 微信授权码登录
 * @param wxCode 微信授权码
 */
export const loginByWeChatCode = async (wxCode: string): Promise<LoginResponse> => {
  // 参考ih-h5项目LoginbyCode.tsx中的微信登录，使用GET方法
  return http.get(AUTH_APIS.WECHAT_LOGIN, { code: wxCode })
}

/**
 * 短信验证码注册（适用于有微信code但微信用户不存在的情况）
 * @param mobile 手机号
 * @param code 短信验证码
 * @param wxCode 微信授权码
 */
export const signupBySMSCode = async (
  mobile: string,
  code: string,
  wxCode: string
): Promise<LoginResponse> => {
  // 参考ih-h5项目LoginService.ts中的signupBySMSCode方法
  return http.post(AUTH_APIS.SMS_SIGNUP, {
    mobile,
    smsCode: code,
    source: 'MP',
    codeType: 'WECHAT',
    wechatCode: wxCode,
  })
}

/**
 * 绑定手机号和微信（手机号用户存在，微信用户不存在）
 * @param mobile 手机号
 * @param wxCode 微信授权码
 * @param smsCode 短信验证码
 */
export const bindPhoneWechat = async (
  mobile: string,
  wxCode: string,
  smsCode: string
): Promise<LoginResponse> => {
  // 参考ih-h5项目LoginService.ts中的bindPhoneWechat方法
  return http.post(AUTH_APIS.WECHAT_BIND, {
    mobile,
    code: wxCode,
    smsCode,
    type: 'MP',
  })
}

/**
 * 获取微信JS配置
 * @param url 当前页面URL
 */
export const getWeChatJSConfig = async (url: string): Promise<WeChatJSConfig> => {
  return http.get(AUTH_APIS.WECHAT_JS_CONFIG, { url })
}

/**
 * 获取微信公众号AppId
 * @param type 类型，默认为MP（公众号）
 */
export const getWeChatAppID = async (type: string = 'MP'): Promise<{ appId: string }> => {
  return http.get(AUTH_APIS.WECHAT_APP_ID, { type })
}

/**
 * 退出登录
 */
export const logout = async (): Promise<void> => {
  return http.post(AUTH_APIS.LOGOUT)
}

/**
 * 微信授权流程
 * 检查URL中的code参数，如果存在则尝试微信登录
 */
export const handleWeChatAuth = async (): Promise<{
  success: boolean
  data?: LoginResponse
  needPhoneBinding?: boolean
  wxCode?: string
  error?: string
}> => {
  // 检查是否在微信浏览器中
  if (!isFromWeiXin()) {
    return { success: false, error: '请在微信中打开' }
  }

  // 获取微信授权码
  const wxCode = getQueryVariableValue('code')
  if (!wxCode) {
    return { success: false, error: '未获取到微信授权码' }
  }

  try {
    // 尝试微信登录
    const loginResult = await loginByWeChatCode(wxCode)
    return { success: true, data: loginResult }
  } catch (error: any) {
    console.log('微信登录失败:', error)

    // 检查是否是用户不存在的错误
    if (error.response?.data?.code === 'error.wechat_user_not_found') {
      return {
        success: false,
        needPhoneBinding: true,
        wxCode,
        error: '需要绑定手机号',
      }
    }

    return {
      success: false,
      error: error.response?.data?.title || '微信登录失败',
    }
  }
}

/**
 * 完整的认证流程
 * 1. 尝试微信授权登录
 * 2. 如果需要，进行手机号绑定
 */
export const performAuthFlow = async (): Promise<{
  success: boolean
  data?: LoginResponse
  needPhoneBinding?: boolean
  wxCode?: string
  error?: string
}> => {
  // 如果在微信中，先尝试微信授权
  if (isFromWeiXin()) {
    return await handleWeChatAuth()
  }

  // 非微信环境，需要手机号登录
  return {
    success: false,
    needPhoneBinding: true,
    error: '请使用手机号登录',
  }
}
