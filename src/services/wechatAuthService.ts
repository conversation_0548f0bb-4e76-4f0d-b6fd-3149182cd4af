/**
 * 微信授权服务
 * 实现完整的微信OAuth2.0授权流程
 * 参考华润项目的微信授权实现
 */

import { http } from '../utils/httpClient'
import {
  getQueryVariableValue,
  isFromWeiXin,
  getHospitalCode,
  getMPAppId,
  setToken,
  clearLocalToken,
} from '../utils/networkUtils'
import { LoginResponse } from './authService'

// 微信相关API端点
export const WECHAT_APIS = {
  // 微信授权接口
  WECHAT_AUTH: '/wechat/auth',
  // 微信登录接口（使用code换取token）
  WECHAT_LOGIN: '/wechat/login',
  // 获取微信JS配置
  WECHAT_JS_CONFIG: '/wechat/mp/jsconfig',
  // 获取微信AppId
  WECHAT_APP_ID: '/wechat/appid',
  // 微信用户信息
  WECHAT_USER_INFO: '/wechat/userinfo',
}

// 微信授权范围
export enum WeChatScope {
  BASIC = 'snsapi_base', // 静默授权，只能获取用户openid
  USERINFO = 'snsapi_userinfo', // 需要用户手动同意，可获取用户基本信息
}

// 微信JS配置接口
export interface WeChatJSConfig {
  appId: string
  timestamp: number
  nonceStr: string
  signature: string
  jsApiList: string[]
  debug?: boolean
}

// 微信用户信息接口
export interface WeChatUserInfo {
  openid: string
  nickname?: string
  sex?: number
  province?: string
  city?: string
  country?: string
  headimgurl?: string
  privilege?: string[]
  unionid?: string
}

// 微信授权状态
export enum WeChatAuthStatus {
  NOT_IN_WECHAT = 'not_in_wechat', // 不在微信环境
  NO_CODE = 'no_code', // 没有授权码
  PROCESSING = 'processing', // 正在处理
  SUCCESS = 'success', // 授权成功
  NEED_PHONE_BINDING = 'need_phone_binding', // 需要绑定手机号
  ERROR = 'error', // 授权失败
}

// 微信授权结果
export interface WeChatAuthResult {
  status: WeChatAuthStatus
  data?: LoginResponse
  wxCode?: string
  wxUserInfo?: WeChatUserInfo
  error?: string
  needPhoneBinding?: boolean
}

/**
 * 获取微信公众号AppId
 * 支持多医院多公众号场景
 */
export const getWeChatAppId = async (type: string = 'MP'): Promise<string> => {
  try {
    // 先尝试从本地缓存获取
    const cachedAppId = getMPAppId()
    if (cachedAppId) {
      console.log('🔍 使用缓存的微信AppId:', cachedAppId)
      return cachedAppId
    }

    // 从服务器获取
    const response = await http.get<{ appId: string }>(WECHAT_APIS.WECHAT_APP_ID, {
      type,
      hospitalCode: getHospitalCode(),
    })

    const appId = response.appId
    if (appId) {
      // 缓存到本地
      const hospitalCode = getHospitalCode()
      if (hospitalCode) {
        localStorage.setItem(hospitalCode, appId)
      }
      console.log('🔍 从服务器获取微信AppId:', appId)
      return appId
    }

    throw new Error('未获取到微信AppId')
  } catch (error: any) {
    console.error('获取微信AppId失败:', error)

    // 降级到配置文件
    const configAppId = window.AppConfig?.WECHAT_CONFIG?.APP_ID
    if (configAppId) {
      console.log('🔍 使用配置文件中的微信AppId:', configAppId)
      return configAppId
    }

    throw new Error('无法获取微信AppId')
  }
}

/**
 * 构建微信授权URL
 * @param scope 授权范围
 * @param redirectUrl 回调地址
 * @param state 状态参数
 */
export const buildWeChatAuthUrl = async (
  scope: WeChatScope = WeChatScope.USERINFO,
  redirectUrl?: string,
  state?: string
): Promise<string> => {
  try {
    const appId = await getWeChatAppId()
    const finalRedirectUrl = redirectUrl || window.location.href
    const finalState = state || `health_assistant_${Date.now()}`

    const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${encodeURIComponent(finalRedirectUrl)}&response_type=code&scope=${scope}&state=${finalState}#wechat_redirect`

    console.log('🔗 构建微信授权URL:', {
      appId,
      redirectUrl: finalRedirectUrl,
      scope,
      state: finalState,
      authUrl,
    })

    return authUrl
  } catch (error: any) {
    console.error('构建微信授权URL失败:', error)
    throw error
  }
}

/**
 * 发起微信授权
 * 自动跳转到微信授权页面
 */
export const initiateWeChatAuth = async (
  scope: WeChatScope = WeChatScope.USERINFO,
  redirectUrl?: string
): Promise<void> => {
  if (!isFromWeiXin()) {
    throw new Error('不在微信环境中，无法进行微信授权')
  }

  try {
    const authUrl = await buildWeChatAuthUrl(scope, redirectUrl)
    console.log('🚀 发起微信授权跳转:', authUrl)
    window.location.href = authUrl
  } catch (error: any) {
    console.error('发起微信授权失败:', error)
    throw error
  }
}

/**
 * 使用微信授权码登录
 * @param wxCode 微信授权码
 */
export const loginByWeChatCode = async (wxCode: string): Promise<LoginResponse> => {
  if (!wxCode) {
    throw new Error('微信授权码不能为空')
  }

  console.log('🔐 使用微信授权码登录:', wxCode)

  try {
    // 参考ih-h5项目LoginbyCode.tsx中的微信登录，使用GET方法
    const response = await http.get<LoginResponse>(WECHAT_APIS.WECHAT_LOGIN, {
      code: wxCode,
    })

    console.log('✅ 微信登录成功:', {
      userId: response.user?.id,
      hasToken: !!response.idToken,
    })

    return response
  } catch (error: any) {
    console.error('微信登录失败:', error)

    // 处理特定错误码
    if (error.response?.data?.code === 'error.wechat_user_not_found') {
      const customError = new Error('微信用户未绑定手机号')
      ;(customError as any).code = 'NEED_PHONE_BINDING'
      ;(customError as any).wxCode = wxCode
      throw customError
    }

    throw error
  }
}

/**
 * 获取微信用户信息
 * @param wxCode 微信授权码
 */
export const getWeChatUserInfo = async (wxCode: string): Promise<WeChatUserInfo> => {
  try {
    const response = await http.get<WeChatUserInfo>(WECHAT_APIS.WECHAT_USER_INFO, {
      code: wxCode,
      type: 'MP',
    })

    console.log('📱 获取微信用户信息成功:', {
      openid: response.openid,
      nickname: response.nickname,
      hasAvatar: !!response.headimgurl,
    })

    return response
  } catch (error: any) {
    console.error('获取微信用户信息失败:', error)
    throw error
  }
}

/**
 * 获取微信JS配置
 * 用于调用微信JS-SDK
 */
export const getWeChatJSConfig = async (url?: string): Promise<WeChatJSConfig> => {
  try {
    const targetUrl = url || window.location.href

    const response = await http.get<WeChatJSConfig>(WECHAT_APIS.WECHAT_JS_CONFIG, {
      url: targetUrl,
      hospitalCode: getHospitalCode(),
    })

    console.log('⚙️ 获取微信JS配置成功:', {
      appId: response.appId,
      timestamp: response.timestamp,
      jsApiList: response.jsApiList,
    })

    return response
  } catch (error: any) {
    console.error('获取微信JS配置失败:', error)
    throw error
  }
}

/**
 * 初始化微信JS-SDK
 * @param jsConfig 微信JS配置
 */
export const initWeChatJSSDK = (jsConfig: WeChatJSConfig): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (typeof window.wx === 'undefined') {
      reject(new Error('微信JS-SDK未加载'))
      return
    }

    console.log('🔧 初始化微信JS-SDK:', jsConfig)

    window.wx.config({
      debug: jsConfig.debug || false,
      appId: jsConfig.appId,
      timestamp: jsConfig.timestamp,
      nonceStr: jsConfig.nonceStr,
      signature: jsConfig.signature,
      jsApiList: jsConfig.jsApiList || [
        'chooseImage',
        'previewImage',
        'uploadImage',
        'downloadImage',
        'onMenuShareTimeline',
        'onMenuShareAppMessage',
      ],
    })

    window.wx.ready(() => {
      console.log('✅ 微信JS-SDK初始化成功')
      resolve()
    })

    window.wx.error((res: any) => {
      console.error('❌ 微信JS-SDK初始化失败:', res)
      reject(new Error(`微信JS-SDK初始化失败: ${res.errMsg}`))
    })
  })
}

/**
 * 检查微信授权状态
 * 检查URL中是否包含微信授权码
 */
export const checkWeChatAuthStatus = (): {
  hasCode: boolean
  code?: string
  state?: string
  error?: string
} => {
  const code = getQueryVariableValue('code')
  const state = getQueryVariableValue('state')
  const error = getQueryVariableValue('error')

  console.log('🔍 检查微信授权状态:', {
    hasCode: !!code,
    code,
    state,
    error,
    inWechat: isFromWeiXin(),
  })

  return {
    hasCode: !!code,
    code,
    state,
    error,
  }
}

/**
 * 处理微信授权回调
 * 自动处理URL中的微信授权码
 */
export const handleWeChatAuthCallback = async (): Promise<WeChatAuthResult> => {
  console.log('🔄 处理微信授权回调')

  // 检查是否在微信环境
  if (!isFromWeiXin()) {
    return {
      status: WeChatAuthStatus.NOT_IN_WECHAT,
      error: '不在微信环境中',
    }
  }

  // 检查授权状态
  const authStatus = checkWeChatAuthStatus()

  if (authStatus.error) {
    return {
      status: WeChatAuthStatus.ERROR,
      error: `微信授权失败: ${authStatus.error}`,
    }
  }

  if (!authStatus.hasCode || !authStatus.code) {
    return {
      status: WeChatAuthStatus.NO_CODE,
      error: '未获取到微信授权码',
    }
  }

  try {
    // 使用授权码登录
    const loginResult = await loginByWeChatCode(authStatus.code)

    // 存储token
    if (loginResult.idToken) {
      setToken(loginResult.idToken)
    }

    // 清理URL参数
    cleanUpUrlParams()

    return {
      status: WeChatAuthStatus.SUCCESS,
      data: loginResult,
      wxCode: authStatus.code,
    }
  } catch (error: any) {
    console.error('微信授权回调处理失败:', error)

    if (error.code === 'NEED_PHONE_BINDING') {
      return {
        status: WeChatAuthStatus.NEED_PHONE_BINDING,
        wxCode: error.wxCode || authStatus.code,
        needPhoneBinding: true,
        error: error.message,
      }
    }

    return {
      status: WeChatAuthStatus.ERROR,
      error: error.response?.data?.title || error.message || '微信授权失败',
    }
  }
}

/**
 * 清理URL中的微信授权参数
 */
export const cleanUpUrlParams = (): void => {
  try {
    const url = new URL(window.location.href)
    url.searchParams.delete('code')
    url.searchParams.delete('state')
    url.searchParams.delete('error')

    // 使用 replaceState 避免触发页面刷新
    window.history.replaceState({}, '', url.toString())

    console.log('🧹 清理URL参数完成')
  } catch (error) {
    console.error('清理URL参数失败:', error)
  }
}

/**
 * 完整的微信授权流程
 * 自动检测环境并处理授权流程
 */
export const performWeChatAuthFlow = async (): Promise<WeChatAuthResult> => {
  console.log('🚀 开始微信授权流程')

  // 检查是否在微信环境
  if (!isFromWeiXin()) {
    return {
      status: WeChatAuthStatus.NOT_IN_WECHAT,
      error: '请在微信中打开',
    }
  }

  // 首先处理可能存在的回调
  const callbackResult = await handleWeChatAuthCallback()

  if (callbackResult.status === WeChatAuthStatus.SUCCESS) {
    return callbackResult
  }

  if (callbackResult.status === WeChatAuthStatus.NEED_PHONE_BINDING) {
    return callbackResult
  }

  // 如果没有授权码，发起授权
  if (callbackResult.status === WeChatAuthStatus.NO_CODE) {
    try {
      await initiateWeChatAuth(WeChatScope.USERINFO)

      return {
        status: WeChatAuthStatus.PROCESSING,
        error: '正在跳转到微信授权页面...',
      }
    } catch (error: any) {
      return {
        status: WeChatAuthStatus.ERROR,
        error: error.message || '发起微信授权失败',
      }
    }
  }

  return callbackResult
}

/**
 * 微信授权重试
 * 清除本地状态并重新发起授权
 */
export const retryWeChatAuth = async (): Promise<void> => {
  console.log('🔄 重试微信授权')

  // 清除本地认证状态
  clearLocalToken()

  // 清理URL参数
  cleanUpUrlParams()

  // 重新发起授权
  await initiateWeChatAuth(WeChatScope.USERINFO)
}

// 微信全局对象类型已在 networkUtils.ts 中声明
