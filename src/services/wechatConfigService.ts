/**
 * 微信JS-SDK配置服务
 * 参考ih-h5项目的微信签名处理机制
 */
import { getWeChatJSConfig, WeChatJSConfig } from './authService'
import wx from "weixin-js-sdk";
import { getCurrentUrlForWechatConfig, isFromWeiXin, getMobileOS } from '../utils/networkUtils'

// 微信配置状态
export enum WeChatConfigStatus {
  IDLE = 'idle',
  LOADING = 'loading',
  SUCCESS = 'success',
  ERROR = 'error'
}

// 微信配置结果接口
export interface WeChatConfigResult {
  status: WeChatConfigStatus
  isReady: boolean
  url?: string
  error?: string
  config?: WeChatJSConfig
}

// 微信配置事件类型
export type WeChatConfigEventType = 'ready' | 'error' | 'retry'

// 微信配置事件监听器
export type WeChatConfigEventListener = (result: WeChatConfigResult) => void

// 事件监听器管理
class WeChatConfigEventManager {
  private listeners: Map<WeChatConfigEventType, WeChatConfigEventListener[]> = new Map()

  on(event: WeChatConfigEventType, listener: WeChatConfigEventListener): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(listener)
  }

  off(event: WeChatConfigEventType, listener: WeChatConfigEventListener): void {
    const eventListeners = this.listeners.get(event)
    if (eventListeners) {
      const index = eventListeners.indexOf(listener)
      if (index > -1) {
        eventListeners.splice(index, 1)
      }
    }
  }

  emit(event: WeChatConfigEventType, result: WeChatConfigResult): void {
    const eventListeners = this.listeners.get(event)
    if (eventListeners) {
      eventListeners.forEach(listener => listener(result))
    }
  }

  clear(): void {
    this.listeners.clear()
  }
}

// 微信配置服务类
class WeChatConfigService {
  private currentStatus: WeChatConfigStatus = WeChatConfigStatus.IDLE
  private currentResult: WeChatConfigResult | null = null
  private eventManager = new WeChatConfigEventManager()
  private retryCount = 0
  private maxRetries = 3
  private retryDelay = 2000

  /**
   * 初始化微信JS-SDK配置
   * @param forceRefresh 是否强制刷新配置
   * @returns Promise<WeChatConfigResult>
   */
  async initWeChatConfig(forceRefresh: boolean = false): Promise<WeChatConfigResult> {
    // 检查是否在微信环境
    if (!isFromWeiXin()) {
      const result: WeChatConfigResult = {
        status: WeChatConfigStatus.ERROR,
        isReady: false,
        error: '不在微信环境中'
      }
      this.updateStatus(result)
      return result
    }

    // 如果已经配置成功且不强制刷新，直接返回
    if (!forceRefresh && this.currentResult?.status === WeChatConfigStatus.SUCCESS) {
      console.log('🔄 微信配置已存在，直接返回')
      return this.currentResult
    }

    // 如果正在配置中，等待完成
    if (this.currentStatus === WeChatConfigStatus.LOADING) {
      console.log('⏳ 微信配置正在进行中，等待完成')
      return this.waitForConfig()
    }

    return this.performConfig()
  }

  /**
   * 执行微信配置
   */
  private async performConfig(): Promise<WeChatConfigResult> {
    this.updateStatus({
      status: WeChatConfigStatus.LOADING,
      isReady: false
    })

    try {
      // 获取当前页面URL（考虑平台差异）
      const url = getCurrentUrlForWechatConfig()
      
      console.log('🔧 开始微信JS-SDK配置:', {
        url,
        platform: getMobileOS() === 2 ? 'iOS' : getMobileOS() === 1 ? 'Android' : 'Other',
        retryCount: this.retryCount
      })

      // 获取微信JS配置
      const jsConfig = await getWeChatJSConfig(url)
      
      // 配置微信JS-SDK
      await this.configWeChatSDK(jsConfig, url)

      const result: WeChatConfigResult = {
        status: WeChatConfigStatus.SUCCESS,
        isReady: true,
        url,
        config: jsConfig
      }

      this.updateStatus(result)
      this.retryCount = 0 // 重置重试次数
      this.eventManager.emit('ready', result)

      console.log('✅ 微信JS-SDK配置成功')
      return result

    } catch (error: any) {
      console.error('❌ 微信JS-SDK配置失败:', error)

      const result: WeChatConfigResult = {
        status: WeChatConfigStatus.ERROR,
        isReady: false,
        error: error.message || '微信配置失败'
      }

      this.updateStatus(result)
      this.eventManager.emit('error', result)

      // 自动重试
      if (this.retryCount < this.maxRetries) {
        this.retryCount++
        console.log(`🔄 微信配置失败，${this.retryDelay}ms后进行第${this.retryCount}次重试`)
        
        setTimeout(() => {
          this.eventManager.emit('retry', result)
          this.performConfig()
        }, this.retryDelay)
      }

      return result
    }
  }

  /**
   * 配置微信JS-SDK
   */
  private configWeChatSDK(jsConfig: WeChatJSConfig, url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      // 检查微信JS-SDK是否已加载
      if (typeof window.wx === 'undefined') {
        reject(new Error('微信JS-SDK未加载'))
        return
      }

      // 配置微信JS-SDK
      window.wx.config({
        debug: import.meta.env.DEV, // 开发环境开启调试
        appId: jsConfig.appId,
        timestamp: jsConfig.timestamp,
        nonceStr: jsConfig.nonceStr,
        signature: jsConfig.signature,
        jsApiList: jsConfig.jsApiList || [
          'chooseImage',
          'previewImage',
          'uploadImage',
          'downloadImage',
          'startRecord',
          'stopRecord',
          'uploadVoice',
          'downloadVoice',
          'playVoice',
          'pauseVoice',
          'stopVoice',
          'onVoicePlayEnd',
          'onVoiceRecordEnd',
          'translateVoice',
          'openLocation',
          'getLocation',
          'scanQRCode',
          'chooseWXPay'
        ]
      })

      // 配置成功回调
      window.wx.ready(() => {
        console.log('🎉 微信JS-SDK ready回调触发')
        resolve()
      })

      // 配置失败回调
      window.wx.error((res: any) => {
        console.error('💥 微信JS-SDK error回调触发:', res)
        reject(new Error(`微信JS-SDK配置错误: ${JSON.stringify(res)}`))
      })
    })
  }

  /**
   * 等待配置完成
   */
  private waitForConfig(): Promise<WeChatConfigResult> {
    return new Promise((resolve) => {
      const checkStatus = () => {
        if (this.currentStatus !== WeChatConfigStatus.LOADING && this.currentResult) {
          resolve(this.currentResult)
        } else {
          setTimeout(checkStatus, 100)
        }
      }
      checkStatus()
    })
  }

  /**
   * 更新配置状态
   */
  private updateStatus(result: WeChatConfigResult): void {
    this.currentStatus = result.status
    this.currentResult = result
  }

  /**
   * 获取当前配置状态
   */
  getCurrentStatus(): WeChatConfigResult | null {
    return this.currentResult
  }

  /**
   * 重置配置状态
   */
  reset(): void {
    this.currentStatus = WeChatConfigStatus.IDLE
    this.currentResult = null
    this.retryCount = 0
    this.eventManager.clear()
  }

  /**
   * 添加事件监听器
   */
  on(event: WeChatConfigEventType, listener: WeChatConfigEventListener): void {
    this.eventManager.on(event, listener)
  }

  /**
   * 移除事件监听器
   */
  off(event: WeChatConfigEventType, listener: WeChatConfigEventListener): void {
    this.eventManager.off(event, listener)
  }
}

// 导出单例实例
export const wechatConfigService = new WeChatConfigService()

// 导出类型和接口
export { WeChatConfigService }

// 全局类型声明
declare global {
  interface Window {
    wx: any
    initUrl?: string
  }
}
