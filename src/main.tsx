import React from 'react'
import ReactDOM from 'react-dom/client'
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom'
import App from './App.tsx'
import { WechatConfigProviderDefault } from './components/WechatConfigProvider'
import './index.css'
import { initPageUrl } from './utils/networkUtils'

// 初始化页面URL（用于iOS平台的微信签名）
initPageUrl()

// 在本地环境中引入vConsole用于微信调试
if (import.meta.env.DEV || import.meta.env.VITE_APP_VERSION?.includes('local')) {
  import('vconsole').then(({ default: VConsole }) => {
    const vConsole = new VConsole({
      theme: 'light',
      defaultPlugins: ['system', 'network', 'element', 'storage'],
      maxLogNumber: 1000
    })
    console.log('📱 vConsole已启用，可在微信中查看调试信息')
  }).catch(err => {
    console.warn('vConsole加载失败:', err)
  })
}

// 全局错误处理
window.addEventListener('error', event => {
  console.error('全局错误捕获:', event.error)
})

window.addEventListener('unhandledrejection', event => {
  console.error('未处理的Promise拒绝:', event.reason)
})

// 开发环境下启用性能监控
if (process.env.NODE_ENV === 'development') {
  console.log('🚀 个人健康助理应用启动中...')
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <BrowserRouter>
      <WechatConfigProviderDefault>
        <App />
      </WechatConfigProviderDefault>
    </BrowserRouter>
  </React.StrictMode>
)
