FROM nginx:1.25-alpine

# 移除默认配置
RUN rm -f /etc/nginx/conf.d/default.conf

# 写入nginx配置文件
COPY ./nginx.conf /etc/nginx/conf.d/default.conf

# 设置工作路径
ENV APP_PATH=/opt/app
WORKDIR ${APP_PATH}

# 添加dist目录到工作路径
ADD ./dist ${APP_PATH}/dist

# 创建版本信息文件
ARG VERSION=latest
RUN echo "window.AppConfig = { \
  version: '$VERSION', \
  appTitle: '健康助理', \
  baseURL: 'https://dify.taihealth.cn/v1', \
  apiKey: 'app-iycGJ8AYIanHB92CvlaQ6yHd', \
  reportAppId: 'app-iycGJ8AYIanHB92CvlaQ6yHd', \
  doctorAppId: 'app-iycGJ8AYIanHB92CvlaQ6yHd' \
};" > ./dist/config.js

# 创建/var/www/frontend并拷贝dist目录中文件到/var/www/frontend
RUN mkdir -p /var/www/frontend && \
    cp -r ./dist/* /var/www/frontend

# 暴露端口
EXPOSE 8060

# 执行
CMD ["nginx", "-g", "daemon off;"]
