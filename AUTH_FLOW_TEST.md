# 认证流程测试指南

## 测试场景

### 1. 微信环境测试

#### 场景1: 首次访问（无token，无微信code）
**预期流程:**
1. 访问 `/` 
2. 检测到无token，在微信环境中
3. 自动跳转到微信授权页面
4. 用户授权后带着code回调
5. 检查微信用户是否存在：
   - 存在：自动登录，保存token，显示首页
   - 不存在：跳转到手机号绑定页面

#### 场景2: 微信授权回调（有code，用户不存在）
**预期流程:**
1. 用户从微信授权回调到 `/?code=xxx&state=xxx`
2. useAuth检测到微信code，尝试登录
3. 返回用户不存在错误，设置状态为`NEED_PHONE_BINDING`
4. AuthProvider检测到状态变化，跳转到 `/phone-binding`
5. 用户输入手机号和验证码
6. 绑定成功后跳转回首页

#### 场景3: 已登录用户（有token）
**预期流程:**
1. 访问任意页面
2. 检测到本地有token
3. 验证token有效性（可选）
4. 直接显示页面内容

### 2. 非微信环境测试

#### 场景1: 首次访问（无token）
**预期流程:**
1. 访问 `/`
2. 检测到无token，非微信环境
3. 直接跳转到 `/phone-binding`
4. 用户输入手机号和验证码登录
5. 登录成功后跳转回首页

### 3. Token过期测试

#### 场景1: API调用时token过期
**预期流程:**
1. 用户正在使用应用
2. 调用API时返回401错误（token过期）
3. HTTP拦截器捕获错误，触发`auth:tokenExpired`事件
4. useAuth监听事件，清除本地状态
5. AuthProvider检测到状态变化，重新走认证流程

## 测试步骤

### 开发环境测试

1. **清除本地存储**
   ```javascript
   localStorage.clear()
   ```

2. **模拟微信环境**
   - 修改User-Agent包含`micromessenger`
   - 或者在微信开发者工具中测试

3. **配置微信测试号**
   - 在`public/config.js`中配置测试公众号AppId
   - 配置授权域名

4. **测试API端点**
   - 确保后端API正常工作
   - 测试微信登录接口
   - 测试手机号验证码接口

### 手动测试流程

1. **清空状态**
   ```
   localStorage.clear()
   刷新页面
   ```

2. **测试微信授权**
   - 在微信中访问应用
   - 观察是否正确跳转到微信授权
   - 授权后观察回调处理

3. **测试手机号绑定**
   - 确保能正常发送验证码
   - 测试验证码验证
   - 测试绑定成功后的跳转

4. **测试登录状态保持**
   - 登录后刷新页面
   - 确保不会重新要求登录

## 调试信息

### 控制台日志

应用会输出详细的调试日志，关键日志包括：
- `🔍 认证状态检查:` - 认证状态变化
- `🔄 跳转到微信授权页面` - 微信授权跳转
- `✅ 用户已认证` - 认证成功
- `🚫 认证失败` - API认证失败
- `⏳ 有微信授权码，等待认证处理` - 等待处理

### 状态检查

在控制台中检查状态：
```javascript
// 检查token
localStorage.getItem('ih_wx_token')

// 检查微信配置
window.AppConfig.WECHAT_CONFIG

// 检查URL参数
new URLSearchParams(window.location.search).get('code')
```

## 常见问题

### 1. 微信授权无限循环
**原因**: 微信配置错误或授权域名未配置
**解决**: 检查config.js中的WECHAT_CONFIG

### 2. 验证码发送失败  
**原因**: 后端短信服务未配置或手机号格式错误
**解决**: 检查API返回和手机号验证

### 3. 页面跳转异常
**原因**: 路由配置错误或状态管理问题
**解决**: 检查AuthProvider和useAuth的状态流转

### 4. Token持久化失败
**原因**: localStorage访问权限或HTTP拦截器配置问题
**解决**: 检查token存储和响应拦截器

## 生产环境注意事项

1. **微信配置**
   - 使用真实的微信公众号AppId
   - 配置正确的授权域名
   - 部署HTTPS环境

2. **安全性**
   - 使用真正的AES加密替换Base64编码
   - 实现token刷新机制
   - 添加请求签名验证

3. **用户体验**
   - 添加loading状态提示
   - 优化错误提示信息
   - 实现离线状态检测